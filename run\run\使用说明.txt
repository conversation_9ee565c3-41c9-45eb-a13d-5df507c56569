===============================================
           扫描数据处理工具 - 使用说明
===============================================

📋 程序功能
-----------
本工具用于处理包含多个scan_*目录的扫描数据，自动转换图像格式并生成标准的目录结构和CSV文件。

🎯 主要特点
-----------
✓ 图形界面操作，简单易用
✓ 自动识别scan_*目录
✓ 智能图像分类和转换
✓ 生成标准格式的pose.csv文件
✓ 实时显示处理进度和日志

📁 支持的数据结构
-----------------
源目录应包含如下结构：
源目录/
├── scan_0708_223037/
│   ├── 0/
│   │   ├── 90.00 ns/
│   │   │   ├── ori_bmp/
│   │   │   └── res_bmp/
│   │   └── params
│   ├── 1/
│   └── ...
├── scan_0708_230611/
└── ...

🖼️ 图像处理规则
----------------
1. NIR + Depth模式（当ori_bmp和res_bmp都有文件时）：
   - ori_bmp中的图片 → 2D-NIR
   - res_bmp中的图片 → 3D-depth

2. RGB模式（当只有ori_bmp有文件时）：
   - ori_bmp中的图片 → 2D-RGB

🚀 使用步骤
-----------
1. 双击运行"扫描数据处理工具.exe"

2. 选择源文件夹：
   - 点击"源文件夹"旁的"浏览"按钮
   - 选择包含scan_*文件夹的目录

3. 选择输出文件夹：
   - 点击"输出文件夹"旁的"浏览"按钮
   - 选择要保存处理结果的目录

4. 开始处理：
   - 点击"开始处理"按钮
   - 观察处理日志，等待完成

5. 查看结果：
   - 处理完成后会弹出提示
   - 在输出目录中查看生成的文件

📂 输出结构
-----------
处理完成后会在输出目录生成：
输出目录/
└── 20250708/              # 根据scan目录名提取的日期
    ├── scandata_1/         # 第一个scan目录的数据
    │   ├── 1/
    │   │   ├── 2D-NIR/
    │   │   │   └── 1.jpg
    │   │   ├── 2D-RGB/
    │   │   └── 3D-depth/
    │   │       └── 1.jpg
    │   ├── 2/
    │   ├── ...
    │   └── pose.csv        # 包含角度和位置信息
    ├── scandata_2/         # 第二个scan目录的数据
    ├── scandata_3/
    └── scandata_4/

📊 CSV文件格式
--------------
pose.csv包含以下列：
- date: 日期（如0708）
- scan: 扫描数据集名称（如scandata_1）
- sequence: 序列号（1, 2, 3...）
- horizontal: 水平角度（如250.00°）
- vertical: 垂直角度（如-52.00°）

⚠️ 注意事项
-----------
1. 确保源目录包含正确格式的scan_*文件夹
2. 处理大量数据时请确保有足够的磁盘空间
3. 处理过程中请不要关闭程序
4. 建议在处理前备份重要数据
5. 图像转换需要时间，请耐心等待

🔧 故障排除
-----------
问题：程序无法启动
解决：检查是否有杀毒软件阻止，尝试以管理员身份运行

问题：找不到scan_*目录
解决：确认源目录中包含以"scan_"开头的文件夹

问题：图像转换失败
解决：检查图像文件是否损坏，确保有足够磁盘空间

问题：处理速度慢
解决：这是正常现象，图像处理需要时间，请耐心等待

问题：CSV文件中角度显示为N/A
解决：检查params文件是否存在且格式正确

📞 技术支持
-----------
如遇到其他问题，请：
1. 查看程序中的处理日志
2. 检查错误信息
3. 确认数据格式是否正确
4. 联系技术支持人员

===============================================
版本：1.0
更新日期：2025年7月11日
===============================================
