import cv2
import numpy as np

# 读取RGB图
img = cv2.imread('D:/fenge/1.bmp')
hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

# 设定绿色（植被）的HSV范围
lower_green = np.array([35, 40, 40])
upper_green = np.array([85, 255, 255])
mask_green = cv2.inRange(hsv, lower_green, upper_green)

# 植被区域（掩膜）
result_vegetation = cv2.bitwise_and(img, img, mask=mask_green)

# 反掩膜，得到地面区域掩膜
mask_ground = cv2.bitwise_not(mask_green)

# 地面区域图像
result_ground = cv2.bitwise_and(img, img, mask=mask_ground)

# 保存三张图（保持原始分辨率）
cv2.imwrite('D:/fenge/output_original.bmp', img)
cv2.imwrite('D:/fenge/output_mask_green.png', mask_green)        # 植被掩膜
cv2.imwrite('D:/fenge/output_segmented_vegetation.bmp', result_vegetation)  # 植被图
cv2.imwrite('D:/fenge/output_mask_ground.png', mask_ground)      # 地面掩膜
cv2.imwrite('D:/fenge/output_segmented_ground.bmp', result_ground)  # 地面图

# 显示图像
cv2.namedWindow("Original Image", cv2.WINDOW_NORMAL)
cv2.namedWindow("Vegetation Mask", cv2.WINDOW_NORMAL)
cv2.namedWindow("Segmented Vegetation", cv2.WINDOW_NORMAL)
cv2.namedWindow("Ground Mask", cv2.WINDOW_NORMAL)
cv2.namedWindow("Segmented Ground", cv2.WINDOW_NORMAL)

cv2.imshow("Original Image", img)
cv2.imshow("Vegetation Mask", mask_green)
cv2.imshow("Segmented Vegetation", result_vegetation)
cv2.imshow("Ground Mask", mask_ground)
cv2.imshow("Segmented Ground", result_ground)

cv2.waitKey(0)
cv2.destroyAllWindows()
