import cv2

img = cv2.imread('D:/fenge/output_segmented_ground.bmp')

def on_mouse(event, x, y, flags, param):
    if event == cv2.EVENT_LBUTTONDOWN:
        b, g, r = img[y, x]
        print(f"位置({x},{y}) 颜色BGR=({b},{g},{r})")

cv2.namedWindow("Image")
cv2.setMouseCallback("Image", on_mouse)

while True:
    cv2.namedWindow("Image")
    cv2.imshow("Image", img)
    if cv2.waitKey(20) & 0xFF == 27:  # 按ESC退出
        break

cv2.destroyAllWindows()
