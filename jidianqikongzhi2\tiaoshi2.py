from pymodbus.client import ModbusSerialClient
from time import sleep

client = ModbusSerialClient(
    port='COM3',        # 替换成你的串口号
    baudrate=9600,
    timeout=1
)

if client.connect():
    print("✅ 串口连接成功")

    slave_id = 0x11  # 从站地址（你的设备地址）

    # 闭合 DO1（写 True 到线圈0）
    print("🔁 正在闭合 DO1...")
    result = client.write_coil(address=0, value=True, slave=slave_id)
    if not result.isError():
        print("✅ DO1 已闭合")
    else:
        print("❌ 写入 DO1 闭合失败")

    sleep(3)

    # 断开 DO1（写 False 到线圈0）
    print("🔁 正在断开 DO1...")
    result = client.write_coil(address=0, value=False, slave=slave_id)
    if not result.isError():
        print("✅ DO1 已断开")
    else:
        print("❌ 写入 DO1 断开失败")

    client.close()
else:
    print("❌ 串口连接失败")
