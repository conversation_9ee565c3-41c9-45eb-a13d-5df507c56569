(['D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\do_control.py'],
 ['D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi'],
 [],
 [('D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.21 (main, Dec 11 2024, 16:35:24) [MSC v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('do_control',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\do_control.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\typing.py', 'PYMODULE'),
  ('contextlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextlib.py',
   'PYMODULE'),
  ('configparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipfile.py', 'PYMODULE'),
  ('argparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gettext.py', 'PYMODULE'),
  ('py_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bz2.py', 'PYMODULE'),
  ('_strptime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_strptime.py',
   'PYMODULE'),
  ('datetime', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\datetime.py', 'PYMODULE'),
  ('calendar', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\calendar.py', 'PYMODULE'),
  ('threading',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fnmatch.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ipaddress.py',
   'PYMODULE'),
  ('email',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\selectors.py',
   'PYMODULE'),
  ('random', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\client.py',
   'PYMODULE'),
  ('hmac', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tempfile.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipimport.py',
   'PYMODULE'),
  ('inspect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ast.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_py_abc.py', 'PYMODULE'),
  ('pymodbus.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\__init__.py',
   'PYMODULE'),
  ('pymodbus.client.udp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\udp.py',
   'PYMODULE'),
  ('pymodbus.transport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\transport\\__init__.py',
   'PYMODULE'),
  ('pymodbus.transport.transport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\transport\\transport.py',
   'PYMODULE'),
  ('pymodbus.transport.serialtransport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\transport\\serialtransport.py',
   'PYMODULE'),
  ('serial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dataclasses.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('pymodbus.pdu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\__init__.py',
   'PYMODULE'),
  ('pymodbus.pdu.pdu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\pdu.py',
   'PYMODULE'),
  ('pymodbus.datastore',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\datastore\\__init__.py',
   'PYMODULE'),
  ('pymodbus.datastore.store',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\datastore\\store.py',
   'PYMODULE'),
  ('pymodbus.datastore.simulator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\datastore\\simulator.py',
   'PYMODULE'),
  ('pymodbus.datastore.context',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\datastore\\context.py',
   'PYMODULE'),
  ('pymodbus.pdu.file_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\file_message.py',
   'PYMODULE'),
  ('pymodbus.pdu.decoders',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\decoders.py',
   'PYMODULE'),
  ('pymodbus.pdu.register_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\register_message.py',
   'PYMODULE'),
  ('pymodbus.pdu.other_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\other_message.py',
   'PYMODULE'),
  ('pymodbus.device',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\device.py',
   'PYMODULE'),
  ('pymodbus.utilities',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\utilities.py',
   'PYMODULE'),
  ('pymodbus.events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\events.py',
   'PYMODULE'),
  ('pymodbus.constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\constants.py',
   'PYMODULE'),
  ('pymodbus.pdu.mei_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\mei_message.py',
   'PYMODULE'),
  ('pymodbus.pdu.diag_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\diag_message.py',
   'PYMODULE'),
  ('pymodbus.pdu.bit_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu\\bit_message.py',
   'PYMODULE'),
  ('pymodbus.logging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\logging.py',
   'PYMODULE'),
  ('pymodbus.framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\__init__.py',
   'PYMODULE'),
  ('pymodbus.framer.tls',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\tls.py',
   'PYMODULE'),
  ('pymodbus.framer.socket',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\socket.py',
   'PYMODULE'),
  ('pymodbus.framer.rtu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\rtu.py',
   'PYMODULE'),
  ('pymodbus.framer.base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\base.py',
   'PYMODULE'),
  ('pymodbus.framer.ascii',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\ascii.py',
   'PYMODULE'),
  ('pymodbus.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\exceptions.py',
   'PYMODULE'),
  ('__future__',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\__future__.py',
   'PYMODULE'),
  ('pymodbus.client.tls',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\tls.py',
   'PYMODULE'),
  ('pymodbus.client.tcp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\tcp.py',
   'PYMODULE'),
  ('pymodbus.client.serial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\serial.py',
   'PYMODULE'),
  ('pymodbus.client.base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\base.py',
   'PYMODULE'),
  ('pymodbus.transaction',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\transaction\\__init__.py',
   'PYMODULE'),
  ('pymodbus.transaction.transaction',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\transaction\\transaction.py',
   'PYMODULE'),
  ('pymodbus.client.mixin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\mixin.py',
   'PYMODULE'),
  ('pymodbus',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\__init__.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE')],
 [('python39.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python39.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('_lzma.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\sip.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\MSVCP140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libffi-7.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('base_library.zip',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\do_control\\base_library.zip',
   'DATA')],
 [('encodings.zlib_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('locale', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\locale.py', 'PYMODULE'),
  ('reprlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\reprlib.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('stat', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stat.py', 'PYMODULE'),
  ('operator', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\operator.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_constants.py',
   'PYMODULE'),
  ('re', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\re.py', 'PYMODULE'),
  ('traceback',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\traceback.py',
   'PYMODULE'),
  ('_bootlocale',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_bootlocale.py',
   'PYMODULE'),
  ('ntpath', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ntpath.py', 'PYMODULE'),
  ('os', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\os.py', 'PYMODULE'),
  ('posixpath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\posixpath.py',
   'PYMODULE'),
  ('heapq', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\heapq.py', 'PYMODULE'),
  ('types', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\types.py', 'PYMODULE'),
  ('weakref', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\weakref.py', 'PYMODULE'),
  ('keyword', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\keyword.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_compile.py',
   'PYMODULE'),
  ('warnings', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\warnings.py', 'PYMODULE'),
  ('functools',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\functools.py',
   'PYMODULE'),
  ('copyreg', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copyreg.py', 'PYMODULE'),
  ('io', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\io.py', 'PYMODULE'),
  ('genericpath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\genericpath.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('enum', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\enum.py', 'PYMODULE'),
  ('linecache',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\linecache.py',
   'PYMODULE'),
  ('sre_parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_parse.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\abc.py', 'PYMODULE'),
  ('codecs', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\codecs.py', 'PYMODULE')])
