#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据Excel文件中的v值对图片进行分组
简化版本，支持手动输入路径
"""

import os
import shutil
import pandas as pd
from pathlib import Path
from collections import defaultdict

def main():
    """主函数"""
    print("=" * 50)
    print("    根据v值对图片进行分组")
    print("=" * 50)

    # 获取用户输入的路径
    print("请输入以下路径信息:")

    excel_path = input("Excel文件路径 (angle_params.xlsx): ").strip().strip('"')
    if not excel_path:
        excel_path = r"F:\daman\0709\scan_0709_102453\angle_params.xlsx"

    photo_dir = input("图片目录路径 (photo_RGB): ").strip().strip('"')
    if not photo_dir:
        photo_dir = r"F:\daman\0709\scan_0709_102453\yangdai\photo_RGB"

    output_dir = input("输出目录路径 (回车使用默认): ").strip().strip('"')
    if not output_dir:
        output_dir = os.path.join(os.path.dirname(excel_path), "按v值分组")

    print(f"\n📊 Excel文件: {excel_path}")
    print(f"🖼️ 图片目录: {photo_dir}")
    print(f"📂 输出目录: {output_dir}")

    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        return

    if not os.path.exists(photo_dir):
        print(f"❌ 图片目录不存在: {photo_dir}")
        return

    try:
        # 读取Excel文件
        print(f"\n📖 正在读取Excel文件...")
        df = pd.read_excel(excel_path)
        print(f"✅ 成功读取 {len(df)} 行数据")
        print(f"📋 列名: {list(df.columns)}")

        # 检查是否有v列
        if 'v' not in df.columns:
            print("❌ Excel文件中没有找到'v'列")
            print("可用列:", list(df.columns))
            return

        # 分析v值
        v_values = df['v'].unique()
        print(f"\n📈 发现 {len(v_values)} 个不同的v值")
        print(f"v值范围: {v_values.min():.3f} ~ {v_values.max():.3f}")

        # 显示每个v值的图片数量
        v_counts = df['v'].value_counts().sort_index()
        print(f"\n📊 各v值对应的图片数量:")
        for v_val, count in v_counts.items():
            print(f"  v={v_val:.3f}: {count}张")

        # 询问是否继续
        choice = input(f"\n是否继续进行分组？(y/n): ").lower().strip()
        if choice not in ['y', 'yes', '是', '']:
            print("操作已取消")
            return

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 检测文件名列
        filename_col = None
        possible_cols = ['filename', 'image_name', 'name', 'file', 'image']

        for col in possible_cols:
            if col in df.columns:
                filename_col = col
                break

        if filename_col is None:
            # 使用第一列作为文件名列
            filename_col = df.columns[0]
            print(f"⚠️ 未找到明确的文件名列，使用第一列: {filename_col}")
        else:
            print(f"📋 使用文件名列: {filename_col}")

        # 按v值分组并复制文件
        print(f"\n📁 开始分组和复制文件...")

        copied_count = 0
        error_count = 0

        # 按v值分组
        for v_value in sorted(v_values):
            v_group = df[df['v'] == v_value]

            # 创建v值文件夹
            v_folder = os.path.join(output_dir, f"v_{v_value:.3f}")
            os.makedirs(v_folder, exist_ok=True)

            print(f"\n📂 处理 v={v_value:.3f} ({len(v_group)}张图片)")

            for _, row in v_group.iterrows():
                filename = str(row[filename_col])

                # 查找源文件
                source_file = None
                extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']

                for ext in extensions:
                    # 尝试直接匹配
                    test_file = os.path.join(photo_dir, filename)
                    if os.path.exists(test_file):
                        source_file = test_file
                        break

                    # 尝试添加扩展名
                    test_file = os.path.join(photo_dir, f"{filename}{ext}")
                    if os.path.exists(test_file):
                        source_file = test_file
                        break

                    # 尝试去掉原扩展名再添加
                    base_name = os.path.splitext(filename)[0]
                    test_file = os.path.join(photo_dir, f"{base_name}{ext}")
                    if os.path.exists(test_file):
                        source_file = test_file
                        break

                if source_file:
                    try:
                        dest_file = os.path.join(v_folder, os.path.basename(source_file))
                        shutil.copy2(source_file, dest_file)
                        copied_count += 1
                        print(f"  ✅ {os.path.basename(source_file)}")
                    except Exception as e:
                        print(f"  ❌ 复制失败 {filename}: {e}")
                        error_count += 1
                else:
                    print(f"  ⚠️ 未找到文件: {filename}")
                    error_count += 1

        # 创建摘要文件
        summary_content = f"""# 图片分组摘要

## 分组信息
- 总分组数: {len(v_values)}个
- 成功复制: {copied_count}张图片
- 失败/未找到: {error_count}张图片

## 各组详情
"""

        for v_value in sorted(v_values):
            v_folder = os.path.join(output_dir, f"v_{v_value:.3f}")
            if os.path.exists(v_folder):
                image_files = [f for f in os.listdir(v_folder)
                             if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'))]
                summary_content += f"- v={v_value:.3f}: {len(image_files)}张图片\n"

        summary_file = os.path.join(output_dir, "分组摘要.txt")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)

        print(f"\n🎉 分组完成!")
        print(f"✅ 成功复制: {copied_count}张图片")
        print(f"❌ 失败/未找到: {error_count}张图片")
        print(f"📁 输出目录: {output_dir}")
        print(f"📄 摘要文件: {summary_file}")

        # 询问是否打开输出文件夹
        choice = input(f"\n是否打开输出文件夹？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '']:
            try:
                os.startfile(output_dir)
            except Exception as e:
                print(f"⚠️ 打开文件夹失败: {e}")

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

    input("\n按回车键退出...")

if __name__ == "__main__":
    main()