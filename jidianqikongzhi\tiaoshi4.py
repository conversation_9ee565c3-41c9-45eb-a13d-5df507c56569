import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QMessageBox, QGroupBox
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient as ModbusClient
from threading import Thread
from time import sleep

class RelayControl(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("继电器 DO1-DO4 控制器")
        self.resize(400, 300)

        self.client = None
        self.slave_id = 0x11  # 设备从站地址

        self.init_ui()
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_status)

    def init_ui(self):
        layout = QVBoxLayout()

        # 串口输入和连接按钮
        port_layout = QHBoxLayout()
        self.port_input = QLineEdit("COM3")
        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.status_label = QLabel("状态：未连接")
        port_layout.addWidget(QLabel("串口号:"))
        port_layout.addWidget(self.port_input)
        port_layout.addWidget(self.connect_btn)
        port_layout.addWidget(self.status_label)

        layout.addLayout(port_layout)

        # DO控制区域
        self.do_group = QGroupBox("DO继电器控制")
        do_layout = QHBoxLayout()

        self.do_buttons = []
        for i in range(4):
            btn = QPushButton(f"DO{i+1} 断开")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, idx=i: self.toggle_do(idx, checked))
            do_layout.addWidget(btn)
            self.do_buttons.append(btn)

        self.do_group.setLayout(do_layout)
        layout.addWidget(self.do_group)

        # 状态刷新按钮
        self.refresh_btn = QPushButton("刷新继电器状态")
        self.refresh_btn.clicked.connect(self.refresh_status)
        layout.addWidget(self.refresh_btn)

        self.setLayout(layout)

    def toggle_connection(self):
        if self.client:
            # 已连接则断开
            self.client.close()
            self.client = None
            self.status_label.setText("状态：未连接")
            self.connect_btn.setText("连接串口")
            self.timer.stop()
            self.set_buttons_enabled(False)
        else:
            port = self.port_input.text()
            self.client = ModbusClient(method='rtu', port=port, baudrate=9600, timeout=1)
            if self.client.connect():
                self.status_label.setText(f"状态：已连接 {port}")
                self.connect_btn.setText("断开串口")
                self.set_buttons_enabled(True)
                self.refresh_status()
                self.timer.start(5000)  # 每5秒刷新一次状态
            else:
                QMessageBox.warning(self, "错误", f"无法连接串口 {port}")
                self.client = None
                self.set_buttons_enabled(False)

    def set_buttons_enabled(self, enabled):
        for btn in self.do_buttons:
            btn.setEnabled(enabled)
        self.refresh_btn.setEnabled(enabled)

    def toggle_do(self, idx, checked):
        if not self.client:
            QMessageBox.warning(self, "错误", "串口未连接")
            self.do_buttons[idx].setChecked(not checked)
            return
        # 写入线圈
        slave_id = self.slave_id
        address = idx
        value = checked

        def write_task():
            result = self.client.write_coil(address=address, value=value, unit=slave_id)
            if result.isError():
                self.show_message(f"写入 DO{idx+1} {'闭合' if value else '断开'} 失败")
                # 回滚按钮状态
                self.do_buttons[idx].setChecked(not value)
            else:
                self.do_buttons[idx].setText(f"DO{idx+1} {'闭合' if value else '断开'}")

        Thread(target=write_task).start()

    def refresh_status(self):
        if not self.client:
            return
        slave_id = self.slave_id

        def read_task():
            result = self.client.read_coils(address=0, count=4, unit=slave_id)
            if result.isError():
                self.show_message("读取继电器状态失败")
                return
            statuses = result.bits
            for i, status in enumerate(statuses):
                def update_btn(i=i, status=status):
                    self.do_buttons[i].setChecked(status)
                    self.do_buttons[i].setText(f"DO{i+1} {'闭合' if status else '断开'}")
                self.do_buttons[i].setEnabled(True)
                self.do_buttons[i].window().windowHandle().scheduleUpdate()  # 保证界面刷新
                self.do_buttons[i].window().update()
                self.do_buttons[i].window().repaint()
                # 在主线程调用更新按钮文字
                self.do_buttons[i].window().metaObject().invokeMethod(
                    self.do_buttons[i], "setChecked", Qt.QueuedConnection, status)
                self.do_buttons[i].setChecked(status)
                self.do_buttons[i].setText(f"DO{i+1} {'闭合' if status else '断开'}")

        Thread(target=read_task).start()

    def show_message(self, text):
        QMessageBox.warning(self, "提示", text)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = RelayControl()
    win.show()
    sys.exit(app.exec_())
