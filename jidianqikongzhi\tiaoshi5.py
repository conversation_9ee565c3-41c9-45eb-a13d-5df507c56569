import sys
from datetime import datetime, time as dtime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QMessageBox, QGroupBox, QTextEdit, QTimeEdit, QGridLayout, QCheckBox
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient as ModbusClient

class RelayControl(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DO继电器智能控制器")
        self.resize(600, 400)

        self.client = None
        self.slave_id = 0x11
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_status)
        self.control_timer = QTimer()
        self.control_timer.timeout.connect(self.schedule_check)

        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()

        # 串口连接区域
        port_layout = QHBoxLayout()
        self.port_input = QLineEdit("COM3")
        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.status_label = QLabel("状态：未连接")
        port_layout.addWidget(QLabel("串口号:"))
        port_layout.addWidget(self.port_input)
        port_layout.addWidget(self.connect_btn)
        port_layout.addWidget(self.status_label)
        main_layout.addLayout(port_layout)

        # DO 控制区域
        self.do_group = QGroupBox("DO继电器控制")
        do_layout = QGridLayout()
        self.do_buttons = []
        self.start_time_edits = []
        self.end_time_edits = []
        self.auto_enable = []

        for i in range(4):
            row = i
            do_label = QLabel(f"DO{i+1}")
            btn = QPushButton("断开")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, idx=i: self.toggle_do(idx, checked))

            auto_chk = QCheckBox("定时")
            start_time = QTimeEdit()
            end_time = QTimeEdit()
            start_time.setDisplayFormat("HH:mm")
            end_time.setDisplayFormat("HH:mm")
            start_time.setTime(dtime(8, 0))
            end_time.setTime(dtime(18, 0))

            self.do_buttons.append(btn)
            self.start_time_edits.append(start_time)
            self.end_time_edits.append(end_time)
            self.auto_enable.append(auto_chk)

            do_layout.addWidget(do_label, row, 0)
            do_layout.addWidget(btn, row, 1)
            do_layout.addWidget(auto_chk, row, 2)
            do_layout.addWidget(QLabel("开始"), row, 3)
            do_layout.addWidget(start_time, row, 4)
            do_layout.addWidget(QLabel("结束"), row, 5)
            do_layout.addWidget(end_time, row, 6)

        self.do_group.setLayout(do_layout)
        main_layout.addWidget(self.do_group)

        # 日志输出
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        main_layout.addWidget(QLabel("日志："))
        main_layout.addWidget(self.log_area)

        self.setLayout(main_layout)

    def toggle_connection(self):
        if self.client:
            self.client.close()
            self.client = None
            self.status_label.setText("状态：未连接")
            self.connect_btn.setText("连接串口")
            self.timer.stop()
            self.control_timer.stop()
            self.set_buttons_enabled(False)
            self.log("⚠️ 串口断开连接")
        else:
            port = self.port_input.text()
            self.client = ModbusClient(method='rtu', port=port, baudrate=9600, timeout=1)
            if self.client.connect():
                self.status_label.setText(f"状态：已连接 {port}")
                self.connect_btn.setText("断开串口")
                self.set_buttons_enabled(True)
                self.timer.start(3000)
                self.control_timer.start(10000)  # 每10秒检查一次定时状态
                self.log(f"✅ 成功连接串口 {port}")
                self.refresh_status()
            else:
                QMessageBox.warning(self, "错误", f"无法连接串口 {port}")
                self.client = None
                self.set_buttons_enabled(False)

    def set_buttons_enabled(self, enabled):
        for btn in self.do_buttons:
            btn.setEnabled(enabled)
        for chk in self.auto_enable:
            chk.setEnabled(enabled)

    def toggle_do(self, idx, checked):
        if not self.client:
            return
        value = checked
        result = self.client.write_coil(address=idx, value=value, unit=self.slave_id)
        if not result.isError():
            self.do_buttons[idx].setText("闭合" if value else "断开")
            self.log(f"DO{idx+1} 手动{'闭合' if value else '断开'}成功")
        else:
            self.log(f"❌ DO{idx+1} 手动{'闭合' if value else '断开'}失败")
            self.do_buttons[idx].setChecked(not value)

    def refresh_status(self):
        if not self.client:
            return
        result = self.client.read_coils(address=0, count=4, unit=self.slave_id)
        if result.isError():
            self.log("❌ 读取继电器状态失败")
            return
        statuses = result.bits

        for i in range(min(len(self.do_buttons), len(statuses))):
            status = statuses[i]
            self.do_buttons[i].setChecked(status)
            self.do_buttons[i].setText("闭合" if status else "断开")

    def schedule_check(self):
        now = datetime.now().time()
        for i in range(4):
            if self.auto_enable[i].isChecked():
                start = self.start_time_edits[i].time().toPyTime()
                end = self.end_time_edits[i].time().toPyTime()
                in_period = False
                if start <= end:
                    in_period = start <= now <= end
                else:
                    in_period = now >= start or now <= end

                try:
                    self.client.write_coil(address=i, value=in_period, unit=self.slave_id)
                    self.do_buttons[i].setChecked(in_period)
                    self.do_buttons[i].setText("闭合" if in_period else "断开")
                    self.log(f"🕒 DO{i+1} 时间段控制 {'闭合' if in_period else '断开'}")
                except Exception as e:
                    self.log(f"❌ DO{i+1} 时间段控制异常: {e}")

    def log(self, msg):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {msg}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = RelayControl()
    win.show()
    sys.exit(app.exec_())
