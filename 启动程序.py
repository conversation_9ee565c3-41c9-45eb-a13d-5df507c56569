#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动继电器和温湿度监控系统启动脚本
"""

import os
import sys
import subprocess

def main():
    print("=" * 50)
    print("    自动继电器和温湿度监控系统")
    print("=" * 50)
    print()
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    program_path = os.path.join(script_dir, "jidianqikongzhi", "自动控制程序.py")
    
    if not os.path.exists(program_path):
        print(f"❌ 错误：找不到程序文件 {program_path}")
        input("按回车键退出...")
        return
    
    print("🚀 正在启动程序...")
    print(f"📁 程序路径: {program_path}")
    print()
    
    try:
        # 切换到脚本目录
        os.chdir(script_dir)
        
        # 启动程序
        subprocess.run([sys.executable, program_path])
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"❌ 启动程序时发生错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
