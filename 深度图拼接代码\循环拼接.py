from PIL import Image
import os

# # 设置裁剪参数
# width_crop = 1188  # 水平方向保留的像素宽度
# x_crop = 340       # 上边裁剪固定像素值
# y_crop = 250        # 下边裁剪像素值
# group_size = 7    # 每组的图片数量
# 设置裁剪参数
width_crop = 1188  # 水平方向保留的像素宽度
x_crop = 200       # 上边裁剪固定像素值
y_crop = 100        # 下边裁剪像素值
group_size = 7    # 每组的图片数量

# 定义输入和输出路径
input_directory = 'F:/daman/0704/scan_0704_224745/yangdai/photo_3D_80ns_crop'
output_directory = 'F:/daman/0704/scan_0704_224745/yangdai/photo_3D_80ns_crop/output'

# 确保输出目录存在
os.makedirs(output_directory, exist_ok=True)

# 获取当前文件夹下所有 .bmp 文件，并按文件名从小到大排序
bmp_files = sorted([f for f in os.listdir(input_directory) if f.endswith('.bmp')],
                   key=lambda x: int(x.split('.')[0]))

# 计算组的数量
total_images = len(bmp_files)
total_groups = (total_images + group_size - 1) // group_size  # 向上取整

# 循环每组处理图片
for group_number in range(total_groups):
    start_idx = group_number * group_size
    end_idx = min(start_idx + group_size, total_images)
    group_files = bmp_files[start_idx:end_idx]

    cropped_images = []

    # 打开并裁剪该组的图片
    for img_file in group_files:
        img_path = os.path.join(input_directory, img_file)
        img = Image.open(img_path)
        # 裁剪右边保留 width_crop 个像素，去掉上边 x_crop 和下边 y_crop 像素
        cropped_img = img.crop((0, x_crop, width_crop, img.height - y_crop))
        cropped_images.append(cropped_img)

    # 获取总的拼接图像高度
    total_height = sum(img.height for img in cropped_images)
    final_width = cropped_images[0].width

    # 创建新图像用于拼接
    final_image = Image.new('RGB', (final_width, total_height))

    # 奇数样带倒序拼接，偶数样带正序拼接
    current_y = 0
    if (group_number + 1) % 2 == 0:
        for img in reversed(cropped_images):
            final_image.paste(img, (0, current_y))
            current_y += img.height
    else:
        for img in cropped_images:
            final_image.paste(img, (0, current_y))
            current_y += img.height

    # 保存输出
    output_image = os.path.join(output_directory, f'样带{group_number + 1}.bmp')
    final_image.save(output_image)
    print(f"样带 {group_number + 1} 的拼接图像已保存至 {output_image}")
