import cv2
import numpy as np
import math

def calculate_LAI(GF, theta_deg=0, k=0.5):
    # """
    # 根据公式 LAI = cos(theta) * ln(GF) / k 计算LAI
    # GF: 叶片孔隙率，范围0~1（必须>0）
    # theta_deg: 入射角度，单位度，默认0度（垂直）
    # k: 消光系数，经验值一般0.4~0.7
    # """
    if GF <= 0 or GF >= 1:
        raise ValueError("GF must be between 0 and 1 (exclusive)")

    theta_rad = math.radians(theta_deg)
    LAI = math.cos(theta_rad) * math.log(GF) / k
    return -LAI  # 取负值使LAI为正

# ---------------------- 主程序 -----------------------

# 读取RGB图
img_path = 'D:/fenge/1.bmp'
img = cv2.imread(img_path)
if img is None:
    raise FileNotFoundError(f"图像路径不存在: {img_path}")

# 转HSV
hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

# 绿色阈值范围，分割植被
lower_green = np.array([40, 11, 128])
upper_green = np.array([63, 47, 174])

mask_green = cv2.inRange(hsv, lower_green, upper_green)

# 得到植被图像
result_vegetation = cv2.bitwise_and(img, img, mask=mask_green)

# 反掩膜得到地面掩膜和图像
mask_ground = cv2.bitwise_not(mask_green)
result_ground = cv2.bitwise_and(img, img, mask=mask_ground)

# 统计像素数
n_total = img.shape[0] * img.shape[1]
n_ground = cv2.countNonZero(mask_ground)
n_vegetation = cv2.countNonZero(mask_green)

# 计算叶片孔隙率 GF = (n_ground + n_stem) / n_total
# 这里简化用 GF = n_ground / n_total，假设杆枝点与地面点合并为地面部分
GF = n_ground / n_total

# 计算LAI
try:
    LAI = calculate_LAI(GF, theta_deg=0, k=0.5)
except ValueError as e:
    print(f"计算LAI失败: {e}")
    LAI = None

# 保存分割结果图像
cv2.imwrite('D:/fenge/output_segmented_vegetation.bmp', result_vegetation)
cv2.imwrite('D:/fenge/output_segmented_ground.bmp', result_ground)
cv2.imwrite('D:/fenge/output_mask_green.png', mask_green)
cv2.imwrite('D:/fenge/output_mask_ground.png', mask_ground)

# 保存LAI结果到文本文件
with open('D:/fenge/LAI_result.txt', 'w') as f:
    f.write(f"图像路径: {img_path}\n")
    f.write(f"图像大小: {img.shape[1]} x {img.shape[0]} (宽 x 高)\n")
    f.write(f"总像素数: {n_total}\n")
    f.write(f"地面像素数: {n_ground}\n")
    f.write(f"植被像素数: {n_vegetation}\n")
    f.write(f"叶片孔隙率GF: {GF:.4f}\n")
    if LAI is not None:
        f.write(f"计算得到的LAI: {LAI:.4f}\n")
    else:
        f.write("LAI计算失败,GF值异常。\n")

print("分割结果图像和LAI值均已保存。")
print(f"LAI = {LAI:.4f}" if LAI is not None else "LAI计算失败。")
