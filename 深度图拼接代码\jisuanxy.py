import cv2
import numpy as np
from PIL import Image

def read_image_unicode(path):
    with Image.open(path) as img:
        img = img.convert('RGB')  # 保留RGB通道用于后面加权拼接
        return np.array(img)

def read_gray_image(path):
    with Image.open(path) as img:
        img = img.convert('L')  # 灰度图用于特征点提取
        return np.array(img)

def compute_crop_offsets(img_path1, img_path2):
    img1 = read_gray_image(img_path1)
    img2 = read_gray_image(img_path2)

    sift = cv2.SIFT_create()
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)

    bf = cv2.BFMatcher()
    matches = bf.knnMatch(des1, des2, k=2)

    good_matches = [m for m, n in matches if m.distance < 0.75 * n.distance]

    if len(good_matches) < 10:
        raise ValueError("匹配点太少，无法可靠估计偏移。")

    dy_list = [kp1[m.queryIdx].pt[1] - kp2[m.trainIdx].pt[1] for m in good_matches]
    dy_median = np.median(dy_list)

    x_crop = int(max(0, dy_median))   # 上裁剪
    y_crop = int(max(0, -dy_median))  # 下裁剪
    return x_crop, y_crop

def vertical_blend(img1, img2, blend_height=30):
    """
    在垂直方向进行加权融合，blend_height 为融合高度（像素）
    """
    h1, w = img1.shape[:2]
    h2 = img2.shape[0]
    final_height = h1 + h2 - blend_height

    blended = np.zeros((final_height, w, 3), dtype=np.uint8)

    # 直接复制 img1 非融合区
    blended[0:h1 - blend_height, :, :] = img1[0:h1 - blend_height, :, :]

    # 加权融合区
    for i in range(blend_height):
        alpha = i / blend_height
        blended[h1 - blend_height + i, :, :] = (
            (1 - alpha) * img1[h1 - blend_height + i, :, :] +
            alpha * img2[i, :, :]
        ).astype(np.uint8)

    # 复制 img2 剩余部分
    blended[h1:, :, :] = img2[blend_height:, :, :]
    return blended

# === 主流程 ===
img1_path = r'G:/大满站数据/0704/scan_0704_221357/yangdai/photo_2D_80ns_A/3.bmp'
img2_path = r'G:/大满站数据/0704/scan_0704_221357/yangdai/photo_2D_80ns_A/4.bmp'

# Step 1：计算裁剪参数
x_crop, y_crop = compute_crop_offsets(img1_path, img2_path)
print(f"推荐裁剪参数：x_crop = {x_crop}, y_crop = {y_crop}")

# Step 2：读取并裁剪图像
img1 = read_image_unicode(img1_path)
img2 = read_image_unicode(img2_path)

width_crop = 1608  # 水平方向保留宽度

img1_cropped = img1[x_crop:img1.shape[0] - y_crop, 0:width_crop, :]
img2_cropped = img2[x_crop:img2.shape[0] - y_crop, 0:width_crop, :]

# Step 3：拼接加权融合
blended_result = vertical_blend(img1_cropped, img2_cropped, blend_height=30)

# Step 4：保存
result_image = Image.fromarray(blended_result)
save_path = r'G:/大满站数据/0704/scan_0704_221357/yangdai/photo_2D_80ns_A/拼接结果.bmp'
result_image.save(save_path)
print(f"融合拼接图已保存到：{save_path}")
