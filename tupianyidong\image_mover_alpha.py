import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QVBoxLayout, QFileDialog, QPushButton, QHBoxLayout
)
from PyQt5.QtGui import QPixmap, QPainter, QImage, QColor, QPen
from PyQt5.QtCore import Qt, QRect
from PIL import Image

class ImageWidget(QLabel):
    def __init__(self, pixmap):
        super().__init__()
        self.original_pixmap = pixmap
        self.offset_x = 0
        self.offset_y = 0
        self.alpha = 255  # 不透明
        self.selected = False
        self.setFixedSize(pixmap.width() + 200, pixmap.height() + 200)

    def move_image(self, dx, dy):
        self.offset_x += dx
        self.offset_y += dy
        self.update()

    def adjust_alpha(self, delta):
        self.alpha = max(0, min(255, self.alpha + delta))
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.fillRect(self.rect(), Qt.white)

        image = self.original_pixmap.toImage().convertToFormat(QImage.Format_ARGB32)
        for y in range(image.height()):
            for x in range(image.width()):
                color = QColor(image.pixel(x, y))
                color.setAlpha(self.alpha)
                image.setPixelColor(x, y, color)

        painter.drawImage(self.offset_x, self.offset_y, image)

        # 画选中边框
        if self.selected:
            pen = QPen(Qt.red, 3)
            painter.setPen(pen)
            painter.drawRect(
                QRect(self.offset_x, self.offset_y,
                      self.original_pixmap.width(),
                      self.original_pixmap.height())
            )


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图像移动 + 透明度控制")
        self.image_widgets = []
        self.selected_index = 0

        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        btn_layout = QHBoxLayout()
        self.load_btn = QPushButton("加载两张图片")
        self.load_btn.clicked.connect(self.load_images)
        btn_layout.addWidget(self.load_btn)
        self.layout.addLayout(btn_layout)

        self.status_label = QLabel("↑↓←→ 移动图像 | +/- 改变透明度 | Tab 切换图像")
        self.layout.addWidget(self.status_label)

    def load_images(self):
        files, _ = QFileDialog.getOpenFileNames(self, "选择两张图片", "", "Images (*.png *.jpg *.bmp)")
        if len(files) != 2:
            self.status_label.setText("请选择恰好两张图片")
            return

        for w in self.image_widgets:
            self.layout.removeWidget(w)
            w.setParent(None)

        self.image_widgets.clear()

        for i, file in enumerate(files):
            qt_image = QPixmap(file)
            widget = ImageWidget(qt_image)
            widget.mousePressEvent = lambda e, idx=i: self.select_image(idx)
            self.layout.addWidget(widget)
            self.image_widgets.append(widget)

        self.select_image(0)

    def select_image(self, index):
        if 0 <= index < len(self.image_widgets):
            for i, w in enumerate(self.image_widgets):
                w.selected = (i == index)
                w.update()
            self.selected_index = index
            self.update_status()

    def keyPressEvent(self, event):
        if not self.image_widgets:
            return

        img = self.image_widgets[self.selected_index]
        key = event.key()

        if key == Qt.Key_Up:
            img.move_image(0, -1)
        elif key == Qt.Key_Down:
            img.move_image(0, 1)
        elif key == Qt.Key_Left:
            img.move_image(-1, 0)
        elif key == Qt.Key_Right:
            img.move_image(1, 0)
        elif key == Qt.Key_Plus or key == Qt.Key_Equal:
            img.adjust_alpha(+5)
        elif key == Qt.Key_Minus or key == Qt.Key_Underscore:
            img.adjust_alpha(-5)
        elif key == Qt.Key_Tab:
            self.select_image((self.selected_index + 1) % len(self.image_widgets))

        self.update_status()

    def update_status(self):
        img = self.image_widgets[self.selected_index]
        self.status_label.setText(
            f"当前图像: {self.selected_index+1} | 偏移: ({img.offset_x}px, {img.offset_y}px) | 透明度: {img.alpha}/255"
        )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())
