import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, 
                             QPushButton, QLabel, QHBoxLayout, QComboBox, 
                             QLineEdit, QTextEdit)
from pymodbus.client import ModbusSerialClient


class DOControlApp(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DO继电器控制 - 带串口输入")

        self.client = None
        self.slave_id = 0x11
        self.client_connected = False

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 串口输入和连接按钮
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("串口号:"))
        self.port_input = QLineEdit("COM3")  # 默认COM3
        self.port_input.setFixedWidth(100)
        port_layout.addWidget(self.port_input)

        self.connect_btn = QPushButton("连接设备")
        self.connect_btn.clicked.connect(self.connect_modbus)
        port_layout.addWidget(self.connect_btn)
        layout.addLayout(port_layout)

        # DO开关按钮（默认禁用，未连接时不能操作）
        self.buttons = []
        for i in range(4):
            btn = QPushButton(f"DO{i+1} 关闭")
            btn.setCheckable(True)
            btn.setEnabled(False)
            btn.clicked.connect(lambda checked, idx=i: self.toggle_do(idx, checked))
            layout.addWidget(btn)
            self.buttons.append(btn)

        # 定时任务设置区
        h_layout = QHBoxLayout()
        h_layout.addWidget(QLabel("定时任务：DO号"))
        self.do_select = QComboBox()
        self.do_select.addItems(["DO1", "DO2", "DO3", "DO4"])
        h_layout.addWidget(self.do_select)

        h_layout.addWidget(QLabel("每天几点（0-23）"))
        self.hour_input = QLineEdit()
        self.hour_input.setFixedWidth(40)
        h_layout.addWidget(self.hour_input)

        self.set_timer_btn = QPushButton("设置定时任务")
        self.set_timer_btn.setEnabled(False)
        self.set_timer_btn.clicked.connect(self.set_timer_task)
        h_layout.addWidget(self.set_timer_btn)

        layout.addLayout(h_layout)

        # 日志显示
        self.log = QTextEdit()
        self.log.setReadOnly(True)
        layout.addWidget(self.log)

        self.setLayout(layout)

    def connect_modbus(self):
        port_name = self.port_input.text().strip()
        if not port_name:
            self.log.append("请输入串口号！")
            return

        # 关闭之前连接（如果有）
        if self.client:
            self.client.close()
            self.client = None
            self.client_connected = False

        self.client = ModbusClient(method='rtu', port=port_name, baudrate=9600, timeout=1)
        if self.client.connect():
            self.log.append(f"成功连接设备，串口: {port_name}")
            self.client_connected = True
            # 启用按钮
            for btn in self.buttons:
                btn.setEnabled(True)
            self.set_timer_btn.setEnabled(True)
        else:
            self.log.append(f"连接失败，串口: {port_name}")
            self.client_connected = False
            for btn in self.buttons:
                btn.setEnabled(False)
            self.set_timer_btn.setEnabled(False)

    def toggle_do(self, idx, checked):
        if not self.client_connected:
            self.log.append("请先连接设备！")
            self.buttons[idx].setChecked(not checked)  # 回退按钮状态
            return

        do_num = idx + 1
        result = self.client.write_coil(idx, checked, unit=self.slave_id)
        if not result.isError():
            state_str = "闭合" if checked else "断开"
            self.buttons[idx].setText(f"DO{do_num} {state_str}")
            self.log.append(f"设置DO{do_num}为{state_str}成功")
        else:
            self.log.append(f"设置DO{do_num}失败")
            self.buttons[idx].setChecked(not checked)  # 失败回退按钮状态

    def set_timer_task(self):
        if not self.client_connected:
            self.log.append("请先连接设备！")
            return

        do_num = self.do_select.currentIndex() + 1
        hour_text = self.hour_input.text()
        if not hour_text.isdigit() or not (0 <= int(hour_text) <= 23):
            self.log.append("请输入合法小时（0-23）")
            return
        hour = int(hour_text)

        cron_expr = f"00{hour:02d}**?*"
        success = write_do_timed_task(self.client, self.slave_id, do_num, enable=0x01, action=0xFF, cron_expression=cron_expr)
        if success:
            self.log.append(f"定时任务设置成功: DO{do_num} 每天{hour}点闭合")
        else:
            self.log.append(f"定时任务设置失败: DO{do_num}")


def write_do_timed_task(client, slave_id, do_num, enable, action, cron_expression):
    base_addr = 0x4300 + (do_num - 1) * 0x40
    data = []
    data.append(enable)
    data.append(0x00)              
    data.append(do_num - 1)        
    data.append(action)
    cron_bytes = [ord(c) for c in cron_expression]
    if len(cron_bytes) < 8:
        cron_bytes += [0x00] * (8 - len(cron_bytes))
    cron_bytes += [0x00] * (124 - 8)
    data += cron_bytes
    registers = []
    for i in range(0, len(data), 2):
        low = data[i]
        high = data[i+1] if (i+1) < len(data) else 0
        reg = low + (high << 8)
        registers.append(reg)
    rr = client.write_registers(address=base_addr, values=registers, unit=slave_id)
    if rr.isError():
        return False
    else:
        return True


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DOControlApp()
    window.show()
    sys.exit(app.exec_())
