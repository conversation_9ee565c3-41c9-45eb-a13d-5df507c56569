import os
import shutil
import re

# 设置原始图像文件夹路径
src_dir = r'F:/daman/0709/scan_0709_102453/yangdai/photo_RGB'

# 设置输出目录，和原始目录同级
dst_base_dir = os.path.join(os.path.dirname(src_dir), '分组结果')
os.makedirs(dst_base_dir, exist_ok=True)

# 提取文件中包含数字的函数
def extract_number(filename):
    match = re.search(r'\d+', filename)
    return int(match.group()) if match else float('inf')

# 获取所有图像文件
image_files = [f for f in os.listdir(src_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif'))]

# 根据数字排序
image_files.sort(key=extract_number)

# 每8个图像为一组
group_size = 8
for i in range(0, len(image_files), group_size):
    group_num = i // group_size + 1
    group_folder = os.path.join(dst_base_dir, f'group_{group_num}')
    os.makedirs(group_folder, exist_ok=True)
    
    for img_file in image_files[i:i + group_size]:
        src_path = os.path.join(src_dir, img_file)
        dst_path = os.path.join(group_folder, img_file)
        shutil.copy2(src_path, dst_path)

print("图像分组完成。")
