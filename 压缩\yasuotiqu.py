import os
import shutil

# 输入输出路径
input_folder = r"G:/scan_0701_192321/yangdai/photo_RGB/yasuo"
output_folder = r"G:/scan_0701_192321/yangdai/photo_RGB/yasuo/selected_range"

# 创建输出文件夹
os.makedirs(output_folder, exist_ok=True)

# 提取参数设置
start_num = 12         # 第一个区间起始编号
images_per_group = 24  # 每组图片数量
gap_between_groups = 48  # 每组之间的起始编号间隔
num_groups = 20         # 提取多少组（你可以改成需要的组数）

# 自动提取多个区间的图片
for group_index in range(num_groups):
    group_start = start_num + group_index * gap_between_groups
    group_end = group_start + images_per_group - 1
    for i in range(group_start, group_end + 1):
        filename = f"{i}.png"
        src_path = os.path.join(input_folder, filename)
        dst_path = os.path.join(output_folder, filename)
        if os.path.exists(src_path):
            shutil.copyfile(src_path, dst_path)
            print(f"已提取：{filename}")
        else:
            print(f"⚠ 未找到：{filename}")

print("所有范围内的 BMP 图像提取完成。")
