{"cells": [{"cell_type": "code", "execution_count": 8, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n"]}], "source": ["import numpy as np\n", "a=np.array([1,2,3,4,5])\n", "print(a)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 10, "outputs": [], "source": ["import cv2\n"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}