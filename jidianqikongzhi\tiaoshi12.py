# 保存为 controller_app.py 或其他你喜欢的名字
import sys
import csv
import os
from datetime import datetime, time as dtime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, QLineEdit,
    QMessageBox, QGroupBox, QTextEdit, QTimeEdit, QGridLayout, QCheckBox, QFileDialog
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient as ModbusClient
import serial.tools.list_ports


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("继电器和温湿度传感器综合控制器")
        self.resize(780, 680)

        self.relay_slave_id = 0x11
        self.sensor_slave_id = 0x01

        self.modbus_client = None
        self.save_data = True
        self.save_path = None
        self.poll_flag = True

        self.relay_action_log = []
        self.run_status_log = []
        self.temp_humi_data = []

        self.create_daily_folder()
        self.init_ui()

        self.poll_timer = QTimer()
        self.poll_timer.timeout.connect(self.poll_devices)

        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.record_run_status)
        self.status_timer_interval_ms = 10 * 1000

        self.save_timer = QTimer()
        self.save_timer.timeout.connect(self.save_data_to_file)
        self.save_timer_interval_ms = 10 * 60 * 1000

        QTimer.singleShot(1000, self.auto_connect_serial)

    def create_daily_folder(self):
        today = datetime.now().strftime("%Y-%m-%d")
        self.save_path = os.path.join(os.getcwd(), "数据记录", today)
        os.makedirs(self.save_path, exist_ok=True)

    def auto_connect_serial(self):
        ports = list(serial.tools.list_ports.comports())
        if not ports:
            self.log("❌ 未检测到可用串口")
            return
        for port in ports:
            try:
                self.port_input.setText(port.device)
                self.log(f"🔍 尝试连接串口: {port.device}")
                self.connect_serial()
                break
            except Exception as e:
                self.log(f"❌ 连接 {port.device} 失败: {e}")

    def init_ui(self):
        layout = QVBoxLayout()

        # 状态组
        status_group = QGroupBox("连接状态")
        status_layout = QVBoxLayout()
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("串口号:"))
        self.port_input = QLineEdit("COM7")
        port_layout.addWidget(self.port_input)
        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        port_layout.addWidget(self.connect_btn)
        self.status_label = QLabel("状态：正在自动连接...")
        port_layout.addWidget(self.status_label)
        status_layout.addLayout(port_layout)

        info_layout = QHBoxLayout()
        self.connection_status = QLabel("🔴 未连接")
        self.data_save_status = QLabel("💾 数据保存: 开启")
        info_layout.addWidget(self.connection_status)
        info_layout.addWidget(self.data_save_status)
        status_layout.addLayout(info_layout)
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # DO继电器控制
        relay_group = QGroupBox("DO继电器控制")
        relay_layout = QGridLayout()
        self.do_buttons = []
        self.auto_checkboxes = []
        self.start_time_edits = []
        self.end_time_edits = []

        for i in range(4):
            relay_layout.addWidget(QLabel(f"DO{i + 1}"), i, 0)
            btn = QPushButton("断开")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, idx=i: self.manual_toggle_do(idx, checked))
            relay_layout.addWidget(btn, i, 1)
            self.do_buttons.append(btn)

            chk = QCheckBox("定时")
            relay_layout.addWidget(chk, i, 2)
            self.auto_checkboxes.append(chk)

            start = QTimeEdit()
            start.setDisplayFormat("HH:mm")
            start.setTime(dtime(8, 0))
            relay_layout.addWidget(QLabel("开始"), i, 3)
            relay_layout.addWidget(start, i, 4)
            self.start_time_edits.append(start)

            end = QTimeEdit()
            end.setDisplayFormat("HH:mm")
            end.setTime(dtime(18, 0))
            relay_layout.addWidget(QLabel("结束"), i, 5)
            relay_layout.addWidget(end, i, 6)
            self.end_time_edits.append(end)

        relay_group.setLayout(relay_layout)
        layout.addWidget(relay_group)

        # 温湿度显示
        sensor_group = QGroupBox("温湿度传感器")
        sensor_layout = QVBoxLayout()
        th_layout = QHBoxLayout()
        self.temp_label = QLabel("🌡️ 温度: -- ℃")
        self.temp_label.setStyleSheet("font-size: 16px; color: #FF6B35;")
        self.humi_label = QLabel("💧 湿度: -- %RH")
        self.humi_label.setStyleSheet("font-size: 16px; color: #4A90E2;")
        th_layout.addWidget(self.temp_label)
        th_layout.addWidget(self.humi_label)
        sensor_layout.addLayout(th_layout)

        info_layout = QHBoxLayout()
        self.last_save_label = QLabel("📁 保存目录: " + self.save_path)
        self.next_save_label = QLabel("⏰ 下次保存: 10分钟后")
        info_layout.addWidget(self.last_save_label)
        info_layout.addWidget(self.next_save_label)
        sensor_layout.addLayout(info_layout)
        sensor_group.setLayout(sensor_layout)
        layout.addWidget(sensor_group)

        # 控制与导出区
        control_group = QGroupBox("数据管理与时间间隔设置")
        control_layout = QHBoxLayout()

        self.export_relay_log_btn = QPushButton("导出继电器动作日志")
        self.export_status_log_btn = QPushButton("导出运行状态日志")
        self.export_temp_humi_btn = QPushButton("导出温湿度数据")
        self.export_relay_log_btn.clicked.connect(self.export_relay_action_log)
        self.export_status_log_btn.clicked.connect(self.export_run_status_log)
        self.export_temp_humi_btn.clicked.connect(self.export_temp_humi_data)

        control_layout.addWidget(self.export_relay_log_btn)
        control_layout.addWidget(self.export_status_log_btn)
        control_layout.addWidget(self.export_temp_humi_btn)

        # 添加间隔设置输入框
        self.temp_interval_input = QLineEdit("10")
        self.status_interval_input = QLineEdit("10")
        self.save_interval_input = QLineEdit("10")
        control_layout.addWidget(QLabel("温湿度(s):"))
        control_layout.addWidget(self.temp_interval_input)
        control_layout.addWidget(QLabel("状态(s):"))
        control_layout.addWidget(self.status_interval_input)
        control_layout.addWidget(QLabel("保存(min):"))
        control_layout.addWidget(self.save_interval_input)
        self.apply_interval_btn = QPushButton("应用间隔设置")
        self.apply_interval_btn.clicked.connect(self.apply_interval_settings)
        control_layout.addWidget(self.apply_interval_btn)

        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        layout.addWidget(QLabel("日志："))
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def toggle_connection(self):
        if self.modbus_client:
            self.disconnect_all()
        else:
            self.connect_serial()

    def connect_serial(self):
        port_name = self.port_input.text().strip()
        try:
            self.modbus_client = ModbusClient(method='rtu', port=port_name, baudrate=9600, timeout=1)
            if not self.modbus_client.connect():
                raise Exception("连接失败")
            self.status_label.setText(f"状态：已连接 {port_name}")
            self.connection_status.setText("🟢 已连接")
            self.connection_status.setStyleSheet("color: green; font-weight: bold;")
            self.connect_btn.setText("断开串口")
            self.set_controls_enabled(True)
            self.log(f"✅ 串口 {port_name} 连接成功")

            self.poll_timer.start(1000)
            self.status_timer.start(self.status_timer_interval_ms)
            self.save_timer.start(self.save_timer_interval_ms)
        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            self.connection_status.setText("🔴 连接失败")
            self.connection_status.setStyleSheet("color: red; font-weight: bold;")
            self.modbus_client = None
            self.set_controls_enabled(False)

    def disconnect_all(self):
        if self.modbus_client:
            self.modbus_client.close()
            self.modbus_client = None
        self.status_label.setText("状态：未连接")
        self.connection_status.setText("🔴 未连接")
        self.connection_status.setStyleSheet("color: red; font-weight: bold;")
        self.connect_btn.setText("连接串口")
        self.set_controls_enabled(False)
        self.poll_timer.stop()
        self.status_timer.stop()
        self.save_timer.stop()
        self.log("⚠️ 串口断开连接")

    def set_controls_enabled(self, enabled):
        for w in self.do_buttons + self.auto_checkboxes + self.start_time_edits + self.end_time_edits:
            w.setEnabled(enabled)
        self.export_relay_log_btn.setEnabled(enabled)
        self.export_status_log_btn.setEnabled(enabled)
        self.export_temp_humi_btn.setEnabled(enabled)

    def manual_toggle_do(self, idx, checked):
        if not self.modbus_client:
            self.log("❌ 未连接")
            return
        try:
            result = self.modbus_client.write_coil(address=idx, value=checked, unit=self.relay_slave_id)
            if result.isError():
                raise Exception("操作失败")
            self.do_buttons[idx].setText("闭合" if checked else "断开")
            self.log(f"DO{idx + 1} 手动{'闭合' if checked else '断开'}")
        except Exception as e:
            self.log(f"❌ DO{idx + 1} 操作失败: {e}")

    def poll_devices(self):
        if self.poll_flag:
            self.read_temp_humi()
        else:
            self.refresh_relay_status()
            self.schedule_relay_control_check()
        self.poll_flag = not self.poll_flag

    def read_temp_humi(self):
        if not self.modbus_client:
            return
        try:
            rr = self.modbus_client.read_holding_registers(0, 2, unit=self.sensor_slave_id)
            if rr.isError():
                raise Exception("读取失败")
            humi = rr.registers[0] / 10.0
            temp = rr.registers[1] / 10.0
            self.temp_label.setText(f"🌡️ 温度: {temp:.1f} ℃")
            self.humi_label.setText(f"💧 湿度: {humi:.1f} %RH")
            self.temp_humi_data.append({'timestamp': datetime.now(), 'temperature': temp, 'humidity': humi})
        except Exception as e:
            self.temp_label.setText("🌡️ 温度: 错误")
            self.humi_label.setText("💧 湿度: 错误")
            self.log(f"❌ 温湿度读取失败: {e}")

    def refresh_relay_status(self):
        if not self.modbus_client:
            return
        try:
            result = self.modbus_client.read_coils(0, 4, unit=self.relay_slave_id)
            if result.isError():
                raise Exception("读取失败")
            for i, status in enumerate(result.bits):
                self.do_buttons[i].blockSignals(True)
                self.do_buttons[i].setChecked(status)
                self.do_buttons[i].setText("闭合" if status else "断开")
                self.do_buttons[i].blockSignals(False)
        except Exception as e:
            self.log(f"❌ 读取继电器状态失败: {e}")

    def schedule_relay_control_check(self):
        now = datetime.now().time()
        for i in range(4):
            if self.auto_checkboxes[i].isChecked():
                start = self.start_time_edits[i].time().toPyTime()
                end = self.end_time_edits[i].time().toPyTime()
                in_period = start <= now <= end if start <= end else now >= start or now <= end
                try:
                    self.modbus_client.write_coil(i, in_period, unit=self.relay_slave_id)
                except Exception as e:
                    self.log(f"❌ DO{i + 1} 自动控制失败: {e}")

    def record_run_status(self):
        now = datetime.now().strftime("%H:%M:%S")
        connected = self.modbus_client is not None
        self.run_status_log.append((now, connected))
        self.log(f"📊 运行状态: {now} - {'正常' if connected else '异常'}")

    def save_data_to_file(self):
        try:
            if not self.temp_humi_data:
                return
            filename = os.path.join(self.save_path, "温湿度记录.csv")
            file_exists = os.path.exists(filename)
            with open(filename, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                if not file_exists:
                    writer.writerow(["时间", "温度(℃)", "湿度(%RH)"])
                for d in self.temp_humi_data:
                    writer.writerow([
                        d['timestamp'].strftime("%Y-%m-%d %H:%M:%S"),
                        f"{d['temperature']:.1f}", f"{d['humidity']:.1f}"
                    ])
            self.temp_humi_data.clear()
            self.log("✅ 自动保存完成")
        except Exception as e:
            self.log(f"❌ 保存失败: {e}")

    def export_relay_action_log(self):
        pass  # 你可以添加已有的导出逻辑

    def export_run_status_log(self):
        pass  # 你可以添加已有的导出逻辑

    def export_temp_humi_data(self):
        pass  # 你可以添加已有的导出逻辑

    def apply_interval_settings(self):
        try:
            temp_interval = int(self.temp_interval_input.text())
            status_interval = int(self.status_interval_input.text())
            save_interval = int(self.save_interval_input.text())

            if temp_interval > 0:
                self.poll_timer.setInterval(temp_interval * 1000)

            if status_interval > 0:
                self.status_timer_interval_ms = status_interval * 1000
                if self.status_timer.isActive():
                    self.status_timer.stop()
                    self.status_timer.start(self.status_timer_interval_ms)

            if save_interval > 0:
                self.save_timer_interval_ms = save_interval * 60 * 1000
                if self.save_timer.isActive():
                    self.save_timer.stop()
                    self.save_timer.start(self.save_timer_interval_ms)

            self.log("✅ 时间间隔已更新")
        except Exception as e:
            self.log(f"❌ 设置间隔失败: {e}")

    def log(self, msg):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {msg}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())
