from PIL import Image
import os

# 设置路径和压缩等级（0~9）
input_folder = r"G:/scan_0701_192321/yangdai/photo_RGB"
output_folder = r"G:/scan_0701_192321/yangdai/photo_RGB/yasuo"
compression_level = 6  # 压缩等级：0（无压缩）到 9（最大压缩）

# 创建输出文件夹（如不存在）
os.makedirs(output_folder, exist_ok=True)

# 提取所有 .bmp 文件，并提取数字部分作为排序依据
bmp_files = []
for filename in os.listdir(input_folder):
    if filename.lower().endswith('.bmp'):
        name_no_ext = os.path.splitext(filename)[0]
        if name_no_ext.isdigit():  # 确保文件名是纯数字
            bmp_files.append((int(name_no_ext), filename))

# 按数字从小到大排序
bmp_files.sort(key=lambda x: x[0])

# 执行转换
for num, filename in bmp_files:
    bmp_path = os.path.join(input_folder, filename)
    png_filename = f"{num}.png"
    png_path = os.path.join(output_folder, png_filename)

    with Image.open(bmp_path) as img:
        img.save(png_path, format='PNG', compress_level=compression_level)

    print(f"已转换：{filename} -> {png_filename}")

print("所有 BMP 图像已按数字顺序成功转换为 PNG。")
