import sys
import serial
import struct
import time
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QVBoxLayout, QMessageBox
)
from PyQt5.QtCore import QTimer

# CRC16 计算函数
def crc16(data: bytes):
    crc = 0xFFFF
    for pos in data:
        crc ^= pos
        for _ in range(8):
            if crc & 0x0001:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc >>= 1
    return crc.to_bytes(2, byteorder='little')

# 解析返回数据
def parse_temp_humi(response: bytes):
    if len(response) < 9:
        raise ValueError("响应数据不足9字节")

    if response[1] != 0x03:
        raise ValueError("功能码错误")

    humidity_raw = int.from_bytes(response[3:5], 'big')
    humidity = humidity_raw / 10.0

    temp_raw = int.from_bytes(response[5:7], 'big', signed=True)
    temperature = temp_raw / 10.0

    return temperature, humidity

# 主界面类
class TempHumiReader(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("温湿度读取器 - RS485 Modbus")
        self.setGeometry(200, 200, 300, 200)

        # 标签
        self.temp_label = QLabel("温度: -- ℃")
        self.humi_label = QLabel("湿度: -- %RH")

        # 控制按钮
        self.start_button = QPushButton("开始读取")
        self.start_button.clicked.connect(self.toggle_reading)

        # 布局
        layout = QVBoxLayout()
        layout.addWidget(self.temp_label)
        layout.addWidget(self.humi_label)
        layout.addWidget(self.start_button)
        self.setLayout(layout)

        # 定时器
        self.timer = QTimer()
        self.timer.setInterval(5000)  # 5秒
        self.timer.timeout.connect(self.read_sensor)

        # 串口配置
        try:
            self.serial = serial.Serial(port='COM3', baudrate=9600, timeout=1)
        except Exception as e:
            QMessageBox.critical(self, "串口错误", f"打开串口失败: {e}")
            sys.exit()

    # 开始/停止控制
    def toggle_reading(self):
        if self.timer.isActive():
            self.timer.stop()
            self.start_button.setText("开始读取")
        else:
            self.timer.start()
            self.start_button.setText("停止读取")

    # 读取并更新界面
    def read_sensor(self):
        try:
            request = bytes([0x01, 0x03, 0x00, 0x00, 0x00, 0x02])
            request += crc16(request)

            self.serial.write(request)
            response = self.serial.read(9)

            temp, humi = parse_temp_humi(response)

            self.temp_label.setText(f"温度: {temp:.1f} ℃")
            self.humi_label.setText(f"湿度: {humi:.1f} %RH")

        except Exception as e:
            self.temp_label.setText("温度: 错误")
            self.humi_label.setText("湿度: 错误")
            print(f"[错误] {e}")

# 主程序入口
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TempHumiReader()
    window.show()
    sys.exit(app.exec_())
