import cv2
import numpy as np
import os
import pandas as pd

# 设置 **包含中文路径** 的文件夹
input_folder = r"D:\代码\张钰润-代码\黑球数据处理代码\差分\AA"  # 你的 10 张灰度图所在的 **中文路径**
output_image = r"D:\代码\张钰润-代码\黑球数据处理代码\差分\AA\平均图像.bmp"  # 输出的多帧平均图像（中文路径）
output_excel = r"D:\代码\张钰润-代码\黑球数据处理代码\差分\AA\直方图统计.xlsx"  # 直方图 Excel 文件（中文路径）

# 读取所有 .bmp 文件
image_files = [f for f in os.listdir(input_folder) if f.endswith(".bmp")]
image_files.sort()  # 确保按文件名排序

if len(image_files) == 0:
    raise ValueError("文件夹中没有 .bmp 格式的灰度图像！")

# 初始化累积图像
sum_image = None
count = 0

# 遍历所有图像进行累积
for filename in image_files:
    image_path = os.path.join(input_folder, filename)

    # 读取图片（支持中文路径）
    image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_GRAYSCALE)

    if image is None:
        print(f"警告：无法加载 {filename}，跳过！")
        continue

    # 初始化 sum_image
    if sum_image is None:
        sum_image = np.zeros_like(image, dtype=np.float32)

    sum_image += image
    count += 1

if count == 0:
    raise ValueError("没有成功加载任何图像，请检查路径！")

# 计算平均图像
average_image = (sum_image / count).astype(np.uint8)

# 保存多帧平均图像（支持中文路径）
cv2.imencode('.bmp', average_image)[1].tofile(output_image)
print(f"✅ 多帧平均完成，结果已保存为 {output_image}")

# 计算 0~128 范围内的直方图
hist, _ = np.histogram(average_image, bins=129, range=(0, 128))

# 创建 Pandas DataFrame 并保存到 Excel
df = pd.DataFrame({"Gray Level": range(0, 128), "Pixel Count": hist[:-1]})
df.to_excel(output_excel, index=False)

print(f"✅ 直方图统计完成，结果已保存为 {output_excel}")
