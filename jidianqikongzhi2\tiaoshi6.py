import sys
import serial
import struct
import csv
from datetime import datetime, time as dtime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, QLineEdit,
    QMessageBox, QGroupBox, QTextEdit, QTimeEdit, QGridLayout, QCheckBox, QFileDialog
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient as ModbusClient

# CRC16 计算函数（Modbus RTU用）
def crc16(data: bytes):
    crc = 0xFFFF
    for pos in data:
        crc ^= pos
        for _ in range(8):
            if crc & 0x0001:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc >>= 1
    return crc.to_bytes(2, byteorder='little')

# 解析温湿度传感器返回数据
def parse_temp_humi(response: bytes):
    if len(response) < 9:
        raise ValueError("响应数据不足9字节")
    if response[1] != 0x03:
        raise ValueError("功能码错误")
    humidity_raw = int.from_bytes(response[3:5], 'big')
    humidity = humidity_raw / 10.0
    temp_raw = int.from_bytes(response[5:7], 'big', signed=True)
    temperature = temp_raw / 10.0
    return temperature, humidity

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("继电器和温湿度传感器综合控制器")
        self.resize(700, 600)

        self.slave_id = 0x11
        self.modbus_client = None
        self.serial_port = None  # 用于温湿度传感器的原始串口读写
        self.save_data = False
        self.save_path = None

        self.init_ui()

        # 定时器：刷新继电器状态
        self.modbus_timer = QTimer()
        self.modbus_timer.timeout.connect(self.refresh_relay_status)

        # 定时器：继电器定时控制检查
        self.control_timer = QTimer()
        self.control_timer.timeout.connect(self.schedule_relay_control_check)

        # 定时器：读取温湿度传感器数据
        self.temp_humi_timer = QTimer()
        self.temp_humi_timer.timeout.connect(self.read_temp_humi)

    def init_ui(self):
        layout = QVBoxLayout()

        # --- 串口连接区域 ---
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("串口号:"))
        self.port_input = QLineEdit("COM3")
        port_layout.addWidget(self.port_input)

        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        port_layout.addWidget(self.connect_btn)

        self.status_label = QLabel("状态：未连接")
        port_layout.addWidget(self.status_label)
        layout.addLayout(port_layout)

        # --- DO继电器控制区域 ---
        relay_group = QGroupBox("DO继电器控制")
        relay_layout = QGridLayout()

        self.do_buttons = []
        self.auto_checkboxes = []
        self.start_time_edits = []
        self.end_time_edits = []

        for i in range(4):
            relay_layout.addWidget(QLabel(f"DO{i+1}"), i, 0)
            btn = QPushButton("断开")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, idx=i: self.manual_toggle_do(idx, checked))
            relay_layout.addWidget(btn, i, 1)
            self.do_buttons.append(btn)

            auto_chk = QCheckBox("定时")
            relay_layout.addWidget(auto_chk, i, 2)
            self.auto_checkboxes.append(auto_chk)

            relay_layout.addWidget(QLabel("开始时间"), i, 3)
            start_time = QTimeEdit()
            start_time.setDisplayFormat("HH:mm")
            start_time.setTime(dtime(8, 0))
            relay_layout.addWidget(start_time, i, 4)
            self.start_time_edits.append(start_time)

            relay_layout.addWidget(QLabel("结束时间"), i, 5)
            end_time = QTimeEdit()
            end_time.setDisplayFormat("HH:mm")
            end_time.setTime(dtime(18, 0))
            relay_layout.addWidget(end_time, i, 6)
            self.end_time_edits.append(end_time)

        relay_group.setLayout(relay_layout)
        layout.addWidget(relay_group)

        # --- 温湿度显示区域 ---
        sensor_group = QGroupBox("温湿度传感器")
        sensor_layout = QHBoxLayout()
        self.temp_label = QLabel("温度: -- ℃")
        self.humi_label = QLabel("湿度: -- %RH")
        sensor_layout.addWidget(self.temp_label)
        sensor_layout.addWidget(self.humi_label)
        sensor_group.setLayout(sensor_layout)
        layout.addWidget(sensor_group)

        # 温湿度保存控制
        save_layout = QHBoxLayout()
        self.select_dir_btn = QPushButton("选择保存目录")
        self.select_dir_btn.clicked.connect(self.select_save_directory)
        save_layout.addWidget(self.select_dir_btn)

        self.start_save_btn = QPushButton("开始保存")
        self.start_save_btn.clicked.connect(self.start_saving)
        self.start_save_btn.setEnabled(False)
        save_layout.addWidget(self.start_save_btn)

        self.stop_save_btn = QPushButton("停止保存")
        self.stop_save_btn.clicked.connect(self.stop_saving)
        self.stop_save_btn.setEnabled(False)
        save_layout.addWidget(self.stop_save_btn)

        layout.addLayout(save_layout)

        # --- 日志显示区域 ---
        layout.addWidget(QLabel("日志："))
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def toggle_connection(self):
        if self.modbus_client or self.serial_port:
            self.disconnect_all()
            return

        port_name = self.port_input.text().strip()
        try:
            # Modbus 串口连接
            self.modbus_client = ModbusClient(method='rtu', port=port_name, baudrate=9600, timeout=1)
            if not self.modbus_client.connect():
                raise Exception("Modbus 设备连接失败")

            # 直接打开原生串口用于温湿度传感器读写
            import serial
            self.serial_port = serial.Serial(port=port_name, baudrate=9600, timeout=1)

            self.status_label.setText(f"状态：已连接 {port_name}")
            self.connect_btn.setText("断开串口")
            self.set_controls_enabled(True)

            self.log(f"✅ 串口 {port_name} 连接成功")

            # 启动定时器
            self.modbus_timer.start(3000)       # 3秒刷新继电器状态
            self.control_timer.start(10000)     # 10秒检查定时控制
            self.temp_humi_timer.start(1000)    # 1秒读取温湿度数据

        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            QMessageBox.warning(self, "连接失败", str(e))
            self.modbus_client = None
            self.serial_port = None
            self.set_controls_enabled(False)

    def disconnect_all(self):
        if self.modbus_client:
            try:
                self.modbus_client.close()
            except:
                pass
            self.modbus_client = None
        if self.serial_port:
            try:
                self.serial_port.close()
            except:
                pass
            self.serial_port = None

        self.status_label.setText("状态：未连接")
        self.connect_btn.setText("连接串口")
        self.set_controls_enabled(False)
        self.modbus_timer.stop()
        self.control_timer.stop()
        self.temp_humi_timer.stop()
        self.log("⚠️ 串口断开连接")

    def set_controls_enabled(self, enabled):
        for btn in self.do_buttons:
            btn.setEnabled(enabled)
        for chk in self.auto_checkboxes:
            chk.setEnabled(enabled)
        for st in self.start_time_edits:
            st.setEnabled(enabled)
        for et in self.end_time_edits:
            et.setEnabled(enabled)
        self.select_dir_btn.setEnabled(enabled)
        self.start_save_btn.setEnabled(enabled and self.save_path is not None)

    # 继电器手动开关
    def manual_toggle_do(self, idx, checked):
        if not self.modbus_client:
            self.log("❌ 串口未连接，无法操作继电器")
            self.do_buttons[idx].setChecked(not checked)
            return

        try:
            result = self.modbus_client.write_coil(address=idx, value=checked, unit=self.slave_id)
            if result.isError():
                raise Exception("写入失败")
            self.do_buttons[idx].setText("闭合" if checked else "断开")
            self.log(f"DO{idx+1} 手动 {'闭合' if checked else '断开'} 成功")
        except Exception as e:
            self.log(f"❌ DO{idx+1} 手动 {'闭合' if checked else '断开'} 失败: {e}")
            self.do_buttons[idx].setChecked(not checked)

    # 定时刷新继电器状态
    def refresh_relay_status(self):
        if not self.modbus_client:
            return
        try:
            result = self.modbus_client.read_coils(address=0, count=4, unit=self.slave_id)
            if result.isError():
                raise Exception("读取失败")
            statuses = result.bits
            for i, status in enumerate(statuses[:4]):
                self.do_buttons[i].blockSignals(True)
                self.do_buttons[i].setChecked(status)
                self.do_buttons[i].setText("闭合" if status else "断开")
                self.do_buttons[i].blockSignals(False)
        except Exception as e:
            self.log(f"❌ 读取继电器状态失败: {e}")

    # 定时检查继电器时间段控制
    def schedule_relay_control_check(self):
        if not self.modbus_client:
            return
        now = datetime.now().time()
        for i in range(4):
            if self.auto_checkboxes[i].isChecked():
                start = self.start_time_edits[i].time().toPyTime()
                end = self.end_time_edits[i].time().toPyTime()
                in_period = False
                if start <= end:
                    in_period = start <= now <= end
                else:
                    # 跨午夜情况
                    in_period = now >= start or now <= end
                try:
                    self.modbus_client.write_coil(address=i, value=in_period, unit=self.slave_id)
                    self.do_buttons[i].blockSignals(True)
                    self.do_buttons[i].setChecked(in_period)
                    self.do_buttons[i].setText("闭合" if in_period else "断开")
                    self.do_buttons[i].blockSignals(False)
                    self.log(f"🕒 DO{i+1} 时间段控制 {'闭合' if in_period else '断开'}")
                except Exception as e:
                    self.log(f"❌ DO{i+1} 时间段控制异常: {e}")

    # 读取温湿度传感器数据
    def read_temp_humi(self):
        if not self.serial_port or not self.serial_port.is_open:
            return
        try:
            # 构造读保持寄存器指令（地址0x01，功能码0x03，起始地址0x0000，读取2个寄存器）
            request = bytes([0x01, 0x03, 0x00, 0x00, 0x00, 0x02]) + crc16(bytes([0x01, 0x03, 0x00, 0x00, 0x00, 0x02]))
            self.serial_port.write(request)
            response = self.serial_port.read(9)
            temp, humi = parse_temp_humi(response)
            self.temp_label.setText(f"温度: {temp:.1f} ℃")
            self.humi_label.setText(f"湿度: {humi:.1f} %RH")

            if self.save_data and self.save_path:
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                filename = f"{self.save_path}/温湿度记录.csv"
                with open(filename, mode='a', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerow([now, f"{temp:.1f}", f"{humi:.1f}"])
        except Exception as e:
            self.temp_label.setText("温度: 错误")
            self.humi_label.setText("湿度: 错误")
            self.log(f"❌ 读取温湿度异常: {e}")

    # 选择保存目录
    def select_save_directory(self):
        folder = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if folder:
            self.save_path = folder
            self.start_save_btn.setEnabled(True)
            self.log(f"📁 选择保存目录: {folder}")

    # 开始保存数据
    def start_saving(self):
        if not self.save_path:
            QMessageBox.warning(self, "保存目录未选择", "请先选择保存目录！")
            return
        self.save_data = True
        self.start_save_btn.setEnabled(False)
        self.stop_save_btn.setEnabled(True)
        self.log("💾 开始保存温湿度数据")

    # 停止保存数据
    def stop_saving(self):
        self.save_data = False
        self.start_save_btn.setEnabled(True)
        self.stop_save_btn.setEnabled(False)
        self.log("🛑 停止保存温湿度数据")

    # 日志输出
    def log(self, msg):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {msg}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())
