#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能网关点云影像文件数据传输脚本
根据数据传输.md文档实现的功能包括：
1. 创建FTP传输session
2. FTP文件上传
3. 传输结束通知
"""

import os
import json
import ftplib
import requests
import time
from pathlib import Path
from typing import Optional, List


class DataTransmissionClient:
    """智能网关数据传输客户端"""
    
    def __init__(self, gateway_ip: str, device_no: str, http_port: int, ftp_username: str = "user1", ftp_password: str = "user1"):
        """
        初始化客户端
        
        Args:
            gateway_ip: 网关服务IP地址
            device_no: 设备编码
            ftp_username: FTP用户名
            ftp_password: FTP密码
        """
        self.gateway_ip = gateway_ip
        self.device_no = device_no
        self.ftp_username = ftp_username
        self.ftp_password = ftp_password
        self.current_session_id = None
        self.http_port = http_port
        # 大文件上传配置
        self.chunk_size = 8192  # 8KB chunks for progress tracking
        self.ftp_timeout = 300   # 5 minutes timeout for large files
    
    def create_ftp_session(self) -> Optional[str]:
        """
        创建FTP传输session
        
        Returns:
            成功返回session_id，失败返回None
        """
        url = f"http://{self.gateway_ip}:{self.http_port}/ftpsession/create"
        params = {"device_no": self.device_no}
        print(url)
        
        try:
            response = requests.get(url, params=params, timeout=60)  # 增加超时时间
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("statusCode") == 200:
                self.current_session_id = result.get("data")
                print(f"成功创建FTP session，session_id: {self.current_session_id}")
                return self.current_session_id
            else:
                print(f"创建FTP session失败: {result.get('message', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"创建FTP session时网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"解析响应JSON失败: {e}")
            return None
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小显示"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024**2:
            return f"{size_bytes/1024:.1f} KB"
        elif size_bytes < 1024**3:
            return f"{size_bytes/(1024**2):.1f} MB"
        else:
            return f"{size_bytes/(1024**3):.1f} GB"
    
    def _upload_file_with_progress(self, ftp: ftplib.FTP, file_path: str, filename: str) -> bool:
        """
        带进度显示的文件上传
        
        Args:
            ftp: FTP连接对象
            file_path: 本地文件路径
            filename: 上传后的文件名
            
        Returns:
            上传成功返回True，否则返回False
        """
        try:
            file_size = os.path.getsize(file_path)
            uploaded_size = 0
            
            print(f"开始上传文件: {filename} ({self._format_file_size(file_size)})")
            
            def upload_callback(data):
                nonlocal uploaded_size
                uploaded_size += len(data)
                progress = (uploaded_size / file_size) * 100
                print(f"\r上传进度: {progress:.1f}% ({self._format_file_size(uploaded_size)}/{self._format_file_size(file_size)})", end="", flush=True)
            
            with open(file_path, 'rb') as file:
                # 对于大文件，使用回调函数显示进度
                if file_size > 10 * 1024 * 1024:  # 大于10MB的文件显示进度
                    ftp.storbinary(f'STOR {filename}', file, callback=upload_callback, blocksize=self.chunk_size)
                else:
                    ftp.storbinary(f'STOR {filename}', file)
                
                print(f"\n✓ 成功上传文件: {filename}")
                return True
                
        except Exception as e:
            print(f"\n✗ 上传文件 {filename} 失败: {e}")
            return False
    
    def upload_files_via_ftp(self, file_paths: List[str], ftp_port: int = 21) -> bool:
        """
        通过FTP上传文件
        
        Args:
            file_paths: 要上传的文件路径列表
            ftp_port: FTP服务端口，默认21
            
        Returns:
            上传成功返回True，否则返回False
        """
        if not self.current_session_id:
            print("错误：未创建FTP session，请先调用create_ftp_session()")
            return False
        
        try:
            # 连接FTP服务器
            ftp = ftplib.FTP()
            ftp.connect(self.gateway_ip, ftp_port, timeout=self.ftp_timeout)
            ftp.login(self.ftp_username, self.ftp_password)
            
            # 设置被动模式，通常对大文件传输更稳定
            ftp.set_pasv(True)
            
            print(f"成功连接到FTP服务器 {self.gateway_ip}:{ftp_port}")
            
            # 切换到目标目录
            target_dir = f"{self.device_no}/{self.current_session_id}"
            
            # 创建目录结构（如果不存在）
            try:
                ftp.cwd(target_dir)
            except ftplib.error_perm:
                # 目录不存在，尝试创建
                self._create_ftp_directory(ftp, target_dir)
                ftp.cwd(target_dir)
            
            print(f"切换到目录: {target_dir}")
            
            # 统计总文件大小
            total_size = 0
            valid_files = []
            for file_path in file_paths:
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    total_size += size
                    valid_files.append((file_path, size))
                    print(f"待上传文件: {os.path.basename(file_path)} ({self._format_file_size(size)})")
                else:
                    print(f"警告：文件 {file_path} 不存在，跳过")
            
            if not valid_files:
                print("没有有效的文件需要上传")
                return False
            
            print(f"\n总共需要上传 {len(valid_files)} 个文件，总大小: {self._format_file_size(total_size)}")
            print("-" * 50)
            
            # 上传文件
            for i, (file_path, file_size) in enumerate(valid_files, 1):
                filename = os.path.basename(file_path)
                print(f"[{i}/{len(valid_files)}] ", end="")
                
                if not self._upload_file_with_progress(ftp, file_path, filename):
                    ftp.quit()
                    return False
                
                print()  # 换行
            
            ftp.quit()
            print("=" * 50)
            print("✓ 所有文件上传完成")
            return True
            
        except ftplib.all_errors as e:
            print(f"FTP操作失败: {e}")
            return False
        except Exception as e:
            print(f"上传过程中发生错误: {e}")
            return False
    
    def _create_ftp_directory(self, ftp: ftplib.FTP, path: str):
        """
        递归创建FTP目录
        
        Args:
            ftp: FTP连接对象
            path: 要创建的目录路径
        """
        dirs = path.strip('/').split('/')
        current_path = ''
        
        for dir_name in dirs:
            current_path += f'/{dir_name}'
            try:
                ftp.cwd(current_path)
            except ftplib.error_perm:
                try:
                    ftp.mkd(current_path)
                    print(f"创建目录: {current_path}")
                except ftplib.error_perm:
                    # 目录可能已存在或没有权限
                    pass
    
    def finish_session(self) -> bool:
        """
        通知传输结束
        
        Returns:
            成功返回True，失败返回False
        """
        if not self.current_session_id:
            print("错误：未创建FTP session，无法结束session")
            return False
        
        url = f"http://{self.gateway_ip}:{self.http_port}/ftpsession/finished"
        params = {
            "device_no": self.device_no,
            "session_id": self.current_session_id
        }
        
        try:
            response = requests.get(url, params=params, timeout=60)  # 增加超时时间
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("statusCode") == 200:
                print(f"✓ 成功结束FTP session: {self.current_session_id}")
                self.current_session_id = None
                return True
            else:
                print(f"结束FTP session失败: {result.get('message', '未知错误')}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"结束FTP session时网络请求失败: {e}")
            return False
        except json.JSONDecodeError as e:
            print(f"解析响应JSON失败: {e}")
            return False
    
    def complete_transmission(self, file_paths: List[str], ftp_port: int = 21) -> bool:
        """
        完整的传输流程：创建session -> 上传文件 -> 结束session
        
        Args:
            file_paths: 要上传的文件路径列表
            ftp_port: FTP服务端口
            
        Returns:
            整个流程成功返回True，否则返回False
        """
        print("🚀 开始数据传输流程...")
        start_time = time.time()
        
        # 1. 创建FTP session
        print("\n1️⃣ 创建FTP传输会话...")
        if not self.create_ftp_session():
            return False
        
        # 2. 上传文件
        print("\n2️⃣ 开始文件上传...")
        if not self.upload_files_via_ftp(file_paths, ftp_port):
            return False
        
        # 3. 结束session
        print("\n3️⃣ 结束传输会话...")
        if not self.finish_session():
            return False
        
        elapsed_time = time.time() - start_time
        print(f"\n🎉 数据传输流程完成! 总用时: {elapsed_time:.1f} 秒")
        return True


def main():
    """
    示例用法 - 适用于100MB zip文件上传
    """
    # 配置参数
    gateway_ip = "*************"
    device_no = "lai123" 
    http_port = 8085 
    ftp_port = 21
    
    # 要上传的文件列表 (支持大型zip文件)
    file_paths = [
        "../20250604.zip",
    ]
    
    # 创建传输客户端
    client = DataTransmissionClient(gateway_ip, device_no, http_port, "user1", "user1")
    
    # 执行完整传输流程
    success = client.complete_transmission(file_paths, ftp_port)
    
    if success:
        print("✅ 所有操作成功完成!")
    else:
        print("❌ 传输过程中出现错误!")


if __name__ == "__main__":
    main() 