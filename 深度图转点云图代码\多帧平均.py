import numpy as np
import cv2 as cv
import os

# 参数设置
delay = 37  # ns
gw = 40  # ns
upper_pct = 0.987
lower_pct = 0.000
depth = 8
c = 3e8

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
output_dir = os.path.join(current_dir, "output")  # 输出文件夹
os.makedirs(output_dir, exist_ok=True)

# 上下限阈值
upper = round(upper_pct * (1 << depth))
lower = round(lower_pct * (1 << depth))

R = delay * 1e-9 * c / 2

# 按序读取 .bmp 文件
file_list = sorted(
    [f for f in os.listdir(current_dir) if f.endswith('.bmp')],
    key=lambda x: int(os.path.splitext(x)[0])
)

n = len(file_list)
if n % 2 != 0:
    print("Warning: The number of .bmp files is odd. The last file will be ignored.")

results = []  # 存储每对的结果

# 遍历每对文件
for i in range(0, n, 2):
    if i + 1 >= n:  # 确保有配对文件
        break

    file1 = os.path.join(current_dir, file_list[i])
    file2 = os.path.join(current_dir, file_list[i + 1])

    # 打印调试信息
    print(f"Processing files: {file1} and {file2}")

    # 读取图片
    src1 = cv.imread(file1, cv.IMREAD_GRAYSCALE)
    src2 = cv.imread(file2, cv.IMREAD_GRAYSCALE)

    # 检查图片是否读取成功
    if src1 is None or src2 is None:
        print(f"Error: Failed to read {file1} or {file2}")
        continue

    # 应用阈值处理
    _, src1 = cv.threshold(src1, lower, 0, cv.THRESH_TOZERO)
    _, src1 = cv.threshold(src1, upper, 0, cv.THRESH_TOZERO_INV)
    _, src2 = cv.threshold(src2, lower, 0, cv.THRESH_TOZERO)
    _, src2 = cv.threshold(src2, upper, 0, cv.THRESH_TOZERO_INV)

    # 生成掩膜
    mask = np.zeros(src1.shape, np.uint8)
    mask[(src1 > 0) & (src2 > 0)] = 255

    # 计算距离
    src1 = src1.astype(np.float64)
    src2 = src2.astype(np.float64)
    dist = np.divide(src1, src2, out=np.zeros_like(src1), where=mask != 0)
    dist = R + gw * 1e-9 * c / 2 / (dist + 1)
    dist[mask == 0] = 0

    # 归一化处理
    dist_norm = cv.normalize(dist, dist, 0, 255, cv.NORM_MINMAX, cv.CV_8U, mask)
    dist_norm[mask == 0] = 0
    res = cv.applyColorMap(dist_norm, cv.COLORMAP_TURBO)
    res[mask == 0] = [0, 0, 0]
    res = cv.cvtC
