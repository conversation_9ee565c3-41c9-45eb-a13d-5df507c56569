import pyautogui
import pyperclip
import time
import subprocess
import os

# Step 1: 启动 AutoStitch
autostitch_path = r"C:\Path\To\AutoStitch.exe"  # 修改为你的 AutoStitch.exe 路径
subprocess.Popen(autostitch_path)
time.sleep(3)  # 等待程序打开

# Step 2: 模拟点击菜单栏 "File" → "Open"
pyautogui.moveTo(50, 50)     # 你需要把鼠标移到 AutoStitch 的菜单位置，例如 File 菜单
pyautogui.click()
time.sleep(1)
pyautogui.moveTo(50, 100)    # "Open" 的位置
pyautogui.click()
time.sleep(2)

# Step 3: 输入要拼接的图片路径（使用剪贴板粘贴）
image_folder = r"C:\Users\<USER>\PicturesToStitch"  # 你的图片目录
pyperclip.copy(image_folder)
pyautogui.hotkey('ctrl', 'l')  # 聚焦地址栏（适用于部分打开文件窗口）
pyautogui.hotkey('ctrl', 'v')  # 粘贴路径
pyautogui.press('enter')
time.sleep(2)

# Step 4: 全选文件并打开
pyautogui.hotkey('ctrl', 'a')
time.sleep(1)
pyautogui.press('enter')

# Step 5: 等待 AutoStitch 自动处理完成
print("正在等待拼接完成...请勿操作鼠标")
time.sleep(60)  # 根据拼接复杂度可调整等待时间

# Step 6: 如果程序自动保存则跳过此步骤
# pyautogui.hotkey('ctrl', 's')  # 如果有保存选项，模拟保存

print("拼接流程完成。")
