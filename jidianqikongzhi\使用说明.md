# 自动继电器和温湿度监控系统使用说明

## 程序功能

这个程序实现了以下自动化功能：

### 🔄 自动连接功能
- **程序启动后自动检测并连接可用串口**
- 无需手动选择串口，程序会自动尝试连接第一个可用的串口
- 连接失败时会显示错误信息，可以手动重新连接

### 📊 实时监控显示
- **温湿度实时显示**：每秒更新温度和湿度数据
- **连接状态显示**：实时显示设备连接状态
- **继电器状态显示**：实时显示4路继电器的开关状态
- **数据保存状态**：显示数据保存功能是否开启

### 📁 自动文件管理
- **自动创建日期文件夹**：程序启动时自动创建以当前日期命名的文件夹（格式：YYYY-MM-DD）
- **跨日期自动切换**：每天零点后自动创建新的日期文件夹
- **文件路径**：`数据记录/YYYY-MM-DD/`

### ⏰ 定时数据保存
- **每10分钟自动保存一次数据**
- **保存的文件类型**：
  - `温湿度记录.csv`：包含时间戳、温度、湿度
  - `设备运行状态.csv`：包含连接状态、传感器状态、继电器状态
  - `继电器状态记录.csv`：包含4路继电器的当前状态

### 🎛️ 继电器控制功能
- **手动控制**：可以手动开关4路继电器
- **定时控制**：可以设置每路继电器的开启和关闭时间
- **状态监控**：实时显示继电器状态变化
- **动作记录**：记录所有继电器的操作历史

## 使用方法

### 1. 启动程序
```bash
python jidianqikongzhi/自动控制程序.py
```

### 2. 程序界面说明

#### 系统状态区域
- **串口号**：显示当前连接的串口
- **连接状态**：🟢已连接 / 🔴未连接
- **数据保存状态**：显示数据保存功能状态

#### 继电器控制区域
- **DO1-DO4**：4路继电器控制按钮
- **定时功能**：勾选"定时"复选框，设置开始和结束时间
- **手动控制**：点击按钮手动开关继电器

#### 温湿度监控区域
- **实时显示**：🌡️温度 和 💧湿度
- **保存信息**：显示数据保存目录和上次保存时间

#### 数据管理区域
- **导出继电器日志**：导出继电器操作历史
- **导出运行状态**：导出设备运行状态记录
- **导出温湿度数据**：导出温湿度历史数据

### 3. 自动功能说明

#### 自动连接
- 程序启动2秒后自动尝试连接串口
- 如果连接失败，可以点击"手动连接"按钮重新连接

#### 自动保存
- 每10分钟自动保存一次数据到CSV文件
- 数据保存在 `数据记录/当前日期/` 文件夹中
- 保存完成后会在日志中显示保存的数据条数

#### 自动日期切换
- 每天零点后程序会自动创建新的日期文件夹
- 数据会保存到新的文件夹中，实现按日期分类存储

## 数据文件说明

### 温湿度记录.csv
```csv
时间,温度(℃),湿度(%RH)
2025-07-11 10:30:00,25.6,60.2
2025-07-11 10:30:01,25.7,60.1
```

### 设备运行状态.csv
```csv
时间,连接状态,温湿度传感器,继电器控制器
2025-07-11 10:30:00,正常,正常,正常
```

### 继电器状态记录.csv
```csv
时间,DO1,DO2,DO3,DO4
2025-07-11 10:30:00,断开,闭合,断开,断开
```

## 注意事项

1. **串口权限**：确保程序有访问串口的权限
2. **设备连接**：确保Modbus设备正确连接到串口
3. **文件权限**：确保程序有在当前目录创建文件夹和文件的权限
4. **内存管理**：程序会自动限制内存中的数据量，避免内存溢出
5. **日志管理**：系统日志会自动限制行数，避免界面卡顿

## 故障排除

### 连接失败
- 检查串口是否被其他程序占用
- 检查设备是否正确连接
- 尝试更换串口号

### 数据保存失败
- 检查磁盘空间是否充足
- 检查文件夹权限
- 检查文件是否被其他程序占用

### 温湿度读取异常
- 检查传感器连接
- 检查Modbus地址设置
- 检查设备通信参数

## 技术参数

- **通信协议**：Modbus RTU
- **波特率**：9600
- **数据保存间隔**：10分钟
- **设备轮询间隔**：1秒
- **状态记录间隔**：10秒
- **继电器从站地址**：0x11
- **传感器从站地址**：0x01
