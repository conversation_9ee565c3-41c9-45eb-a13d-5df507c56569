# import cv2
# import numpy as np
#
# # 读取图片（确保文件路径正确）
# image_with_laser = cv2.imread('laser_onB.bmp', cv2.IMREAD_GRAYSCALE)  # 开激光
# image_without_laser = cv2.imread('laser_off1.bmp', cv2.IMREAD_GRAYSCALE)  # 未开激光
#
# # 检查是否成功加载
# if image_with_laser is None or image_without_laser is None:
#     raise ValueError("无法加载图片，请检查文件路径！")
#
# # 确保两张图片尺寸一致
# if image_with_laser.shape != image_without_laser.shape:
#     raise ValueError("两张图片尺寸不一致，无法进行差分运算！")
#
# # 进行差分计算
# diff = cv2.absdiff(image_with_laser, image_without_laser)
#
# # 自适应阈值处理（高斯加权）
# thresholded_adaptive = cv2.adaptiveThreshold(
#     diff, 255,  # 输出最大值为 255
#     cv2.ADAPTIVE_THRESH_GAUSSIAN_C,  # 自适应方式：高斯加权
#     cv2.THRESH_BINARY,  # 阈值类型：二值化
#     11, 2  # (blockSize=11, C=2) 滑动窗口大小 11x11，减去 2 作为阈值
# )
#
# # 保存结果
# cv2.imwrite('threshold_adaptiveB1.bmp', thresholded_adaptive)
#
# print("自适应阈值滤波完成，结果已保存为 threshold_adaptive.bmp")

import cv2
import numpy as np

# 读取图片（确保文件路径正确）
image_with_laser = cv2.imread('laser_on_avr_B.bmp', cv2.IMREAD_GRAYSCALE)  # 开激光
image_without_laser = cv2.imread('laser_off_avr_A.bmp', cv2.IMREAD_GRAYSCALE)  # 未开激光

# 检查是否成功加载
if image_with_laser is None or image_without_laser is None:
    raise ValueError("无法加载图片，请检查文件路径！")

# 确保两张图片尺寸一致
if image_with_laser.shape != image_without_laser.shape:
    raise ValueError("两张图片尺寸不一致，无法进行差分运算！")

# 进行差分计算
diff = cv2.absdiff(image_with_laser, image_without_laser)

# 设定阈值
T = 4  # 阈值

# 阈值处理：低于 T 的设为 0，高于等于 T 的保持原值
thresholded = np.where(diff < T, 0, diff).astype(np.uint8)

# 保存结果
cv2.imwrite('threshold_customBA.bmp', thresholded)
print("自定义阈值滤波完成，结果已保存为 threshold_custom.bmp")



