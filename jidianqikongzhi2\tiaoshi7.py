import sys
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QLineEdit
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient as ModbusClient

class MultiDeviceControl(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("多设备RS485管理器")
        self.resize(600, 400)

        # 设备地址
        self.sensor_slave_id = 1       # 温湿度传感器设备地址
        self.relay_slave_id = 0x11     # 继电器设备地址

        # 初始化Modbus客户端
        self.client = None

        # UI 部分
        layout = QVBoxLayout()

        # 串口输入和连接
        h_layout = QHBoxLayout()
        h_layout.addWidget(QLabel("串口号:"))
        self.port_edit = QLineEdit("COM3")
        h_layout.addWidget(self.port_edit)
        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        h_layout.addWidget(self.connect_btn)
        self.status_label = QLabel("状态：未连接")
        h_layout.addWidget(self.status_label)
        layout.addLayout(h_layout)

        # 温湿度显示
        self.temp_label = QLabel("温度: -- ℃")
        self.humi_label = QLabel("湿度: -- %RH")
        layout.addWidget(self.temp_label)
        layout.addWidget(self.humi_label)

        # 继电器控制按钮
        relay_layout = QHBoxLayout()
        self.relay_buttons = []
        for i in range(4):
            btn = QPushButton(f"继电器 DO{i+1} 断开")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, idx=i: self.control_relay(idx, checked))
            btn.setEnabled(False)
            relay_layout.addWidget(btn)
            self.relay_buttons.append(btn)
        layout.addLayout(relay_layout)

        # 日志输出
        layout.addWidget(QLabel("日志:"))
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        self.setLayout(layout)

        # 定时器，轮询任务
        self.timer = QTimer()
        self.timer.timeout.connect(self.poll_devices)

        self.is_connected = False

    def toggle_connection(self):
        if self.is_connected:
            # 断开连接
            if self.client:
                self.client.close()
            self.client = None
            self.is_connected = False
            self.status_label.setText("状态：未连接")
            self.connect_btn.setText("连接串口")
            self.timer.stop()
            for btn in self.relay_buttons:
                btn.setEnabled(False)
            self.log("⚠️ 串口断开连接")
        else:
            port = self.port_edit.text()
            self.client = ModbusClient(method='rtu', port=port, baudrate=9600, timeout=1)
            if self.client.connect():
                self.is_connected = True
                self.status_label.setText(f"状态：已连接 {port}")
                self.connect_btn.setText("断开串口")
                for btn in self.relay_buttons:
                    btn.setEnabled(True)
                self.timer.start(3000)  # 3秒轮询一次
                self.log(f"✅ 成功连接串口 {port}")
            else:
                self.log(f"❌ 连接串口 {port} 失败")
                self.client = None

    def poll_devices(self):
        if not self.is_connected or not self.client:
            return
        self.read_sensor()
        self.read_relay_status()

    def read_sensor(self):
        try:
            # 读取温湿度传感器（假设地址寄存器和格式跟之前代码相同）
            # 这里示例读保持寄存器03功能码，地址0x00，长度2寄存器
            rr = self.client.read_holding_registers(address=0, count=2, unit=self.sensor_slave_id)
            if rr.isError():
                self.log(f"❌ 读取温湿度传感器失败")
                self.temp_label.setText("温度: 错误")
                self.humi_label.setText("湿度: 错误")
                return
            # 解析数据示例
            humidity_raw = rr.registers[0]
            temp_raw = rr.registers[1]
            humidity = humidity_raw / 10.0
            temperature = temp_raw / 10.0
            self.temp_label.setText(f"温度: {temperature:.1f} ℃")
            self.humi_label.setText(f"湿度: {humidity:.1f} %RH")
            self.log(f"🌡 温度: {temperature:.1f} ℃, 湿度: {humidity:.1f} %RH")
        except Exception as e:
            self.log(f"❌ 读取温湿度异常: {e}")

    def read_relay_status(self):
        try:
            rr = self.client.read_coils(address=0, count=4, unit=self.relay_slave_id)
            if rr.isError():
                self.log("❌ 读取继电器状态失败")
                return
            for i in range(4):
                status = rr.bits[i]
                btn = self.relay_buttons[i]
                btn.blockSignals(True)
                btn.setChecked(status)
                btn.setText(f"继电器 DO{i+1} {'闭合' if status else '断开'}")
                btn.blockSignals(False)
        except Exception as e:
            self.log(f"❌ 读取继电器状态异常: {e}")

    def control_relay(self, idx, checked):
        if not self.is_connected or not self.client:
            self.log("⚠️ 串口未连接，不能控制继电器")
            self.relay_buttons[idx].setChecked(not checked)  # 回滚按钮
            return
        try:
            rr = self.client.write_coil(address=idx, value=checked, unit=self.relay_slave_id)
            if rr.isError():
                self.log(f"❌ 控制继电器 DO{idx+1} {'闭合' if checked else '断开'} 失败")
                self.relay_buttons[idx].setChecked(not checked)
            else:
                self.relay_buttons[idx].setText(f"继电器 DO{idx+1} {'闭合' if checked else '断开'}")
                self.log(f"✅ 控制继电器 DO{idx+1} {'闭合' if checked else '断开'} 成功")
        except Exception as e:
            self.log(f"❌ 控制继电器异常: {e}")
            self.relay_buttons[idx].setChecked(not checked)

    def log(self, msg):
        now = datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{now}] {msg}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MultiDeviceControl()
    win.show()
    sys.exit(app.exec_())
