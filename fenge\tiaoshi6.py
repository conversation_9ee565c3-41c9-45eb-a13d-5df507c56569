import cv2
import numpy as np
import os
import math

def calculate_LAI(GF, theta_deg=0, k=0.5):
    if GF <= 0 or GF >= 1:
        raise ValueError("GF must be between 0 and 1 (exclusive)")
    theta_rad = math.radians(theta_deg)
    LAI = math.cos(theta_rad) * math.log(GF) / k
    return -LAI

# ---------------------- 主程序 -----------------------

# 输入路径
rgb_path = 'D:/fenge/RGB.png'
depth_path = 'D:/fenge/3D.png'

# 输出目录和前缀
output_dir = os.path.dirname(rgb_path)
img_name = os.path.splitext(os.path.basename(rgb_path))[0]

# 读取图像
rgb = cv2.imread(rgb_path)
depth = cv2.imread(depth_path)

if rgb is None:
    raise FileNotFoundError(f"RGB图像不存在: {rgb_path}")
if depth is None:
    raise FileNotFoundError(f"3D图像不存在: {depth_path}")

# 检查尺寸一致性，若不一致，resize depth图到rgb大小
if rgb.shape[:2] != depth.shape[:2]:
    print("警告：RGB图与3D图尺寸不一致，自动调整3D图大小")
    depth = cv2.resize(depth, (rgb.shape[1], rgb.shape[0]))

# 转HSV分割绿色（植被）
hsv = cv2.cvtColor(rgb, cv2.COLOR_BGR2HSV)
lower_green = np.array([35, 40, 40])
upper_green = np.array([85, 255, 255])
mask_green = cv2.inRange(hsv, lower_green, upper_green)
mask_ground = cv2.bitwise_not(mask_green)

# 分割RGB图植被与地面（可选，保存查看）
result_vegetation = cv2.bitwise_and(rgb, rgb, mask=mask_green)
result_ground = cv2.bitwise_and(rgb, rgb, mask=mask_ground)
cv2.imwrite(os.path.join(output_dir, f'{img_name}_segmented_vegetation.png'), result_vegetation)
cv2.imwrite(os.path.join(output_dir, f'{img_name}_segmented_ground.png'), result_ground)

# 将掩膜作用于3D图像
vegetation_3D = cv2.bitwise_and(depth, depth, mask=mask_green)
ground_3D = cv2.bitwise_and(depth, depth, mask=mask_ground)

# 保存分割后的3D图
cv2.imwrite(os.path.join(output_dir, f"{img_name}_3D_segmented_vegetation.png"), vegetation_3D)
cv2.imwrite(os.path.join(output_dir, f"{img_name}_3D_segmented_ground.png"), ground_3D)

# 统计像素数，计算叶片孔隙率 GF = 地面点数 / 总像素数
n_total = rgb.shape[0] * rgb.shape[1]
n_ground = cv2.countNonZero(mask_ground)
GF = n_ground / n_total

# 计算LAI
try:
    LAI = calculate_LAI(GF, theta_deg=0, k=0.5)
except ValueError as e:
    print(f"计算LAI失败: {e}")
    LAI = None

# 保存LAI结果
with open(os.path.join(output_dir, f"{img_name}_LAI_result.txt"), 'w') as f:
    f.write(f"RGB图像路径: {rgb_path}\n")
    f.write(f"3D图像路径: {depth_path}\n")
    f.write(f"图像大小: {rgb.shape[1]} x {rgb.shape[0]} (宽 x 高)\n")
    f.write(f"总像素数: {n_total}\n")
    f.write(f"地面像素数: {n_ground}\n")
    f.write(f"叶片孔隙率GF: {GF:.4f}\n")
    if LAI is not None:
        f.write(f"计算得到的LAI: {LAI:.4f}\n")
    else:
        f.write("LAI计算失败，GF值异常。\n")

print(f"分割图像与LAI计算结果已保存到 {output_dir}")
print(f"LAI = {LAI:.4f}" if LAI is not None else "LAI计算失败。")
