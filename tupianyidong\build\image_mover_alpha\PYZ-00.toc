('D:\\代码\\张钰润-代码\\黑球数据处理代码\\tupianyidong\\build\\image_mover_alpha\\PYZ-00.pyz',
 [('PIL',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\doctest.py', 'PYMODULE'),
  ('email',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hmac.py', 'PYMODULE'),
  ('html',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\random.py', 'PYMODULE'),
  ('readline',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('runpy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\secrets.py', 'PYMODULE'),
  ('selectors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\signal.py', 'PYMODULE'),
  ('smtplib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\textwrap.py', 'PYMODULE'),
  ('threading',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\threading.py',
   'PYMODULE'),
  ('threadpoolctl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('token', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\uu.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\webbrowser.py',
   'PYMODULE'),
  ('xml', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipimport.py',
   'PYMODULE')])
