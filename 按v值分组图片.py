#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据angle_params.xlsx中的v值对图片进行分组
将v值相同的图片放到同一个文件夹中
"""

import os
import shutil
import pandas as pd
from pathlib import Path
from collections import defaultdict

def read_angle_params(excel_path):
    """读取angle_params.xlsx文件"""
    try:
        df = pd.read_excel(excel_path)
        print(f"✅ 成功读取Excel文件: {excel_path}")
        print(f"📊 数据行数: {len(df)}")
        print(f"📋 列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return None

def analyze_v_values(df):
    """分析v值的分布"""
    if 'v' not in df.columns:
        print("❌ Excel文件中没有找到'v'列")
        return None
    
    v_values = df['v'].unique()
    v_counts = df['v'].value_counts().sort_index()
    
    print(f"\n📈 v值分析:")
    print(f"🔢 唯一v值数量: {len(v_values)}")
    print(f"📊 v值范围: {v_values.min():.2f} ~ {v_values.max():.2f}")
    print(f"\n📋 各v值对应的图片数量:")
    for v_val, count in v_counts.items():
        print(f"  v={v_val:.2f}: {count}张图片")
    
    return v_counts

def detect_filename_column(df):
    """自动检测文件名列"""
    possible_columns = ['filename', 'image_name', 'name', 'file', 'image', 'photo']

    for col in possible_columns:
        if col in df.columns:
            return col

    # 如果没有找到明确的文件名列，检查第一列是否包含文件名
    first_col = df.columns[0]
    sample_value = str(df[first_col].iloc[0])

    # 检查是否包含常见的图片扩展名或看起来像文件名
    if any(ext in sample_value.lower() for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']) or \
       any(char.isdigit() for char in sample_value):
        return first_col

    return None

def group_images_by_v(df, photo_dir, output_dir):
    """根据v值对图片进行分组"""
    photo_path = Path(photo_dir)
    output_path = Path(output_dir)

    if not photo_path.exists():
        print(f"❌ 图片目录不存在: {photo_dir}")
        return False

    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)

    # 检测文件名列
    filename_col = detect_filename_column(df)
    if filename_col is None:
        print("❌ 无法检测到文件名列")
        print("📋 可用列名:", list(df.columns))
        filename_col = input("请手动输入文件名列名: ").strip()
        if filename_col not in df.columns:
            print("❌ 指定的列名不存在")
            return False

    print(f"📋 使用文件名列: {filename_col}")

    # 按v值分组
    v_groups = defaultdict(list)

    for _, row in df.iterrows():
        filename = str(row[filename_col])
        v_value = row['v']
        v_groups[v_value].append(filename)
    
    print(f"\n📁 开始创建分组文件夹...")
    
    copied_count = 0
    error_count = 0
    
    for v_value, filenames in v_groups.items():
        # 创建v值对应的文件夹
        v_folder = output_path / f"v_{v_value:.2f}"
        v_folder.mkdir(exist_ok=True)
        
        print(f"\n📂 处理 v={v_value:.2f} 组 ({len(filenames)}张图片)")
        
        for filename in filenames:
            # 查找图片文件
            source_file = None
            
            # 尝试不同的文件扩展名
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
            
            for ext in extensions:
                # 尝试原文件名
                potential_file = photo_path / f"{filename}{ext}"
                if potential_file.exists():
                    source_file = potential_file
                    break
                
                # 尝试去掉扩展名后再加扩展名
                base_name = Path(filename).stem
                potential_file = photo_path / f"{base_name}{ext}"
                if potential_file.exists():
                    source_file = potential_file
                    break
            
            if source_file:
                try:
                    # 复制文件到对应的v值文件夹
                    dest_file = v_folder / source_file.name
                    shutil.copy2(source_file, dest_file)
                    copied_count += 1
                    print(f"  ✅ {source_file.name}")
                except Exception as e:
                    print(f"  ❌ 复制失败 {filename}: {e}")
                    error_count += 1
            else:
                print(f"  ⚠️ 未找到文件: {filename}")
                error_count += 1
    
    print(f"\n🎉 分组完成!")
    print(f"✅ 成功复制: {copied_count}张图片")
    print(f"❌ 失败/未找到: {error_count}张图片")
    print(f"📁 输出目录: {output_path}")
    
    return True

def create_group_summary(output_dir):
    """创建分组摘要文件"""
    output_path = Path(output_dir)
    
    summary_lines = ["# 图片分组摘要\n\n"]
    summary_lines.append("根据v值对图片进行分组的结果:\n\n")
    
    total_images = 0
    group_count = 0
    
    # 遍历所有v值文件夹
    for v_folder in sorted(output_path.glob("v_*")):
        if v_folder.is_dir():
            images = list(v_folder.glob("*"))
            image_count = len([f for f in images if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']])
            
            if image_count > 0:
                v_value = v_folder.name.replace('v_', '')
                summary_lines.append(f"## {v_folder.name}\n")
                summary_lines.append(f"- v值: {v_value}\n")
                summary_lines.append(f"- 图片数量: {image_count}张\n")
                summary_lines.append(f"- 文件夹路径: {v_folder}\n\n")
                
                total_images += image_count
                group_count += 1
    
    summary_lines.insert(2, f"- 总分组数: {group_count}个\n")
    summary_lines.insert(3, f"- 总图片数: {total_images}张\n\n")
    
    # 保存摘要文件
    summary_file = output_path / "分组摘要.md"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.writelines(summary_lines)
    
    print(f"📄 已创建分组摘要: {summary_file}")

def main():
    """主函数"""
    print("=" * 60)
    print("    根据v值对图片进行分组")
    print("=" * 60)
    
    # 设置路径（您可以修改这些路径）
    base_dir = r"F:\daman\0709\scan_0709_102453"
    excel_file = os.path.join(base_dir, "angle_params.xlsx")
    photo_dir = os.path.join(base_dir, "yangdai", "photo_RGB")
    output_dir = os.path.join(base_dir, "按v值分组")
    
    print(f"📁 基础目录: {base_dir}")
    print(f"📊 Excel文件: {excel_file}")
    print(f"🖼️ 图片目录: {photo_dir}")
    print(f"📂 输出目录: {output_dir}")
    print()
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        
        # 让用户手动输入路径
        print("\n请手动输入正确的路径:")
        excel_file = input("Excel文件路径: ").strip().strip('"')
        if not os.path.exists(excel_file):
            print("❌ 文件仍然不存在，程序退出")
            return
        
        # 重新设置相关路径
        base_dir = os.path.dirname(excel_file)
        photo_dir = input("图片目录路径: ").strip().strip('"')
        output_dir = input("输出目录路径 (回车使用默认): ").strip().strip('"')
        if not output_dir:
            output_dir = os.path.join(base_dir, "按v值分组")
    
    if not os.path.exists(photo_dir):
        print(f"❌ 图片目录不存在: {photo_dir}")
        return
    
    # 读取Excel文件
    df = read_angle_params(excel_file)
    if df is None:
        return
    
    # 分析v值分布
    v_counts = analyze_v_values(df)
    if v_counts is None:
        return
    
    # 询问是否继续
    choice = input(f"\n是否继续进行分组？(y/n): ").lower().strip()
    if choice not in ['y', 'yes', '是', '']:
        print("操作已取消")
        return
    
    # 执行分组
    if group_images_by_v(df, photo_dir, output_dir):
        create_group_summary(output_dir)
        
        # 询问是否打开输出文件夹
        choice = input(f"\n是否打开输出文件夹？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '']:
            try:
                os.startfile(output_dir)
            except Exception as e:
                print(f"⚠️ 打开文件夹失败: {e}")
    
    print("\n✅ 程序执行完成!")

if __name__ == "__main__":
    main()
