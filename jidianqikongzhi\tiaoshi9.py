import sys
import csv
from datetime import datetime, time as dtime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, QLineEdit,
    QMessageBox, QGroupBox, QTextEdit, QTimeEdit, QGridLayout, QCheckBox, QFileDialog
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient as ModbusClient


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("继电器和温湿度传感器综合控制器")
        self.resize(700, 650)

        self.relay_slave_id = 0x11  # 继电器从站ID
        self.sensor_slave_id = 0x01  # 温湿度传感器从站ID

        self.modbus_client = None
        self.save_data = False
        self.save_path = None

        self.poll_flag = True  # True 读温湿度，False 读继电器

        # 继电器动作日志列表，格式：[(时间, DO编号, 动作)]
        self.relay_action_log = []

        # 运行状态日志列表，格式：[(时间, 是否正常)]
        self.run_status_log = []

        self.init_ui()

        # 轮询定时器，避免串口冲突
        self.poll_timer = QTimer()
        self.poll_timer.timeout.connect(self.poll_devices)

        # 运行状态定时记录（每10秒）
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.record_run_status)

        # 开始状态记录定时器的默认不开启，连接串口后启动
        self.status_timer_interval_ms = 10 * 1000

    def init_ui(self):
        layout = QVBoxLayout()

        # --- 串口连接区域 ---
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("串口号:"))
        self.port_input = QLineEdit("COM7")
        port_layout.addWidget(self.port_input)

        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        port_layout.addWidget(self.connect_btn)

        self.status_label = QLabel("状态：未连接")
        port_layout.addWidget(self.status_label)
        layout.addLayout(port_layout)

        # --- DO继电器控制区域 ---
        relay_group = QGroupBox("DO继电器控制")
        relay_layout = QGridLayout()

        self.do_buttons = []
        self.auto_checkboxes = []
        self.start_time_edits = []
        self.end_time_edits = []

        for i in range(4):
            relay_layout.addWidget(QLabel(f"DO{i + 1}"), i, 0)
            btn = QPushButton("断开")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, idx=i: self.manual_toggle_do(idx, checked))
            relay_layout.addWidget(btn, i, 1)
            self.do_buttons.append(btn)

            auto_chk = QCheckBox("定时")
            relay_layout.addWidget(auto_chk, i, 2)
            self.auto_checkboxes.append(auto_chk)

            relay_layout.addWidget(QLabel("开始时间"), i, 3)
            start_time = QTimeEdit()
            start_time.setDisplayFormat("HH:mm")
            start_time.setTime(dtime(8, 0))
            relay_layout.addWidget(start_time, i, 4)
            self.start_time_edits.append(start_time)

            relay_layout.addWidget(QLabel("结束时间"), i, 5)
            end_time = QTimeEdit()
            end_time.setDisplayFormat("HH:mm")
            end_time.setTime(dtime(18, 0))
            relay_layout.addWidget(end_time, i, 6)
            self.end_time_edits.append(end_time)

        relay_group.setLayout(relay_layout)
        layout.addWidget(relay_group)

        # --- 温湿度显示区域 ---
        sensor_group = QGroupBox("温湿度传感器")
        sensor_layout = QHBoxLayout()
        self.temp_label = QLabel("温度: -- ℃")
        self.humi_label = QLabel("湿度: -- %RH")
        sensor_layout.addWidget(self.temp_label)
        sensor_layout.addWidget(self.humi_label)
        sensor_group.setLayout(sensor_layout)
        layout.addWidget(sensor_group)

        # 温湿度保存控制
        save_layout = QHBoxLayout()
        self.select_dir_btn = QPushButton("选择保存目录")
        self.select_dir_btn.clicked.connect(self.select_save_directory)
        save_layout.addWidget(self.select_dir_btn)

        self.start_save_btn = QPushButton("开始保存")
        self.start_save_btn.clicked.connect(self.start_saving)
        self.start_save_btn.setEnabled(False)
        save_layout.addWidget(self.start_save_btn)

        self.stop_save_btn = QPushButton("停止保存")
        self.stop_save_btn.clicked.connect(self.stop_saving)
        self.stop_save_btn.setEnabled(False)
        save_layout.addWidget(self.stop_save_btn)

        # 新增按钮：导出继电器动作日志
        self.export_relay_log_btn = QPushButton("导出继电器动作日志")
        self.export_relay_log_btn.clicked.connect(self.export_relay_action_log)
        self.export_relay_log_btn.setEnabled(False)
        save_layout.addWidget(self.export_relay_log_btn)

        # 新增按钮：导出运行状态日志
        self.export_status_log_btn = QPushButton("导出运行状态日志")
        self.export_status_log_btn.clicked.connect(self.export_run_status_log)
        self.export_status_log_btn.setEnabled(False)
        save_layout.addWidget(self.export_status_log_btn)

        layout.addLayout(save_layout)

        # --- 日志显示区域 ---
        layout.addWidget(QLabel("日志："))
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def toggle_connection(self):
        if self.modbus_client:
            self.disconnect_all()
            return

        port_name = self.port_input.text().strip()
        try:
            self.modbus_client = ModbusClient(method='rtu', port=port_name, baudrate=9600, timeout=1)
            if not self.modbus_client.connect():
                raise Exception("Modbus 设备连接失败")

            self.status_label.setText(f"状态：已连接 {port_name}")
            self.connect_btn.setText("断开串口")
            self.set_controls_enabled(True)
            self.log(f"✅ 串口 {port_name} 连接成功")

            self.poll_timer.start(1000)  # 每1秒轮询一次
            self.status_timer.start(self.status_timer_interval_ms)  # 每10秒记录一次运行状态
            self.export_relay_log_btn.setEnabled(True)
            self.export_status_log_btn.setEnabled(True)

        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            QMessageBox.warning(self, "连接失败", str(e))
            self.modbus_client = None
            self.set_controls_enabled(False)
            self.export_relay_log_btn.setEnabled(False)
            self.export_status_log_btn.setEnabled(False)

    def disconnect_all(self):
        if self.modbus_client:
            try:
                self.modbus_client.close()
            except:
                pass
            self.modbus_client = None

        self.status_label.setText("状态：未连接")
        self.connect_btn.setText("连接串口")
        self.set_controls_enabled(False)
        self.poll_timer.stop()
        self.status_timer.stop()
        self.log("⚠️ 串口断开连接")
        self.export_relay_log_btn.setEnabled(False)
        self.export_status_log_btn.setEnabled(False)

    def set_controls_enabled(self, enabled):
        for btn in self.do_buttons:
            btn.setEnabled(enabled)
        for chk in self.auto_checkboxes:
            chk.setEnabled(enabled)
        for st in self.start_time_edits:
            st.setEnabled(enabled)
        for et in self.end_time_edits:
            et.setEnabled(enabled)
        self.select_dir_btn.setEnabled(enabled)
        self.start_save_btn.setEnabled(enabled and self.save_path is not None)
        self.export_relay_log_btn.setEnabled(enabled)
        self.export_status_log_btn.setEnabled(enabled)

    # 手动开关继电器DO
    def manual_toggle_do(self, idx, checked):
        if not self.modbus_client:
            self.log("❌ 串口未连接，无法操作继电器")
            self.do_buttons[idx].setChecked(not checked)
            return

        try:
            result = self.modbus_client.write_coil(address=idx, value=checked, unit=self.relay_slave_id)
            if result.isError():
                raise Exception("写入失败")
            self.do_buttons[idx].setText("闭合" if checked else "断开")
            self.log(f"DO{idx + 1} 手动 {'闭合' if checked else '断开'} 成功")

            # 记录继电器动作日志
            self.record_relay_action(idx + 1, "手动闭合" if checked else "手动断开")

        except Exception as e:
            self.log(f"❌ DO{idx + 1} 手动 {'闭合' if checked else '断开'} 失败: {e}")
            self.do_buttons[idx].setChecked(not checked)

    # 轮询函数，交替访问温湿度和继电器
    def poll_devices(self):
        if self.poll_flag:
            self.read_temp_humi()
        else:
            self.refresh_relay_status()
            self.schedule_relay_control_check()
        self.poll_flag = not self.poll_flag

    # 读取温湿度传感器数据
    def read_temp_humi(self):
        if not self.modbus_client:
            return
        try:
            rr = self.modbus_client.read_holding_registers(address=0, count=2, unit=self.sensor_slave_id)
            if rr.isError():
                raise Exception("读取温湿度失败")
            humidity_raw = rr.registers[0]
            temp_raw = rr.registers[1]
            humidity = humidity_raw / 10.0
            temperature = temp_raw / 10.0
            self.temp_label.setText(f"温度: {temperature:.1f} ℃")
            self.humi_label.setText(f"湿度: {humidity:.1f} %RH")

            if self.save_data and self.save_path:
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                filename = f"{self.save_path}/温湿度记录.csv"
                with open(filename, mode='a', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerow([now, f"{temperature:.1f}", f"{humidity:.1f}"])
            self.log(f"🌡 温度: {temperature:.1f} ℃, 湿度: {humidity:.1f} %RH")
        except Exception as e:
            self.temp_label.setText("温度: 错误")
            self.humi_label.setText("湿度: 错误")
            self.log(f"❌ 读取温湿度异常: {e}")

    # 刷新继电器状态
    def refresh_relay_status(self):
        if not self.modbus_client:
            return
        try:
            result = self.modbus_client.read_coils(address=0, count=4, unit=self.relay_slave_id)
            if result.isError():
                raise Exception("读取继电器状态失败")
            statuses = result.bits
            for i in range(min(4, len(statuses))):
                old_status = self.do_buttons[i].isChecked()
                new_status = statuses[i]
                if old_status != new_status:
                    # 继电器状态发生变化，记录日志
                    action_str = "自动闭合" if new_status else "自动断开"
                    self.record_relay_action(i + 1, action_str)

                self.do_buttons[i].blockSignals(True)
                self.do_buttons[i].setChecked(new_status)
                self.do_buttons[i].setText("闭合" if new_status else "断开")
                self.do_buttons[i].blockSignals(False)
        except Exception as e:
            self.log(f"❌ 读取继电器状态失败: {e}")

    # 继电器定时控制检查
    def schedule_relay_control_check(self):
        if not self.modbus_client:
            return
        now = datetime.now().time()
        for i in range(4):
            if self.auto_checkboxes[i].isChecked():
                start = self.start_time_edits[i].time().toPyTime()
                end = self.end_time_edits[i].time().toPyTime()
                in_period = False
                if start <= end:
                    in_period = start <= now <= end
                else:
                    in_period = now >= start or now <= end  # 跨天处理
                try:
                    self.modbus_client.write_coil(address=i, value=in_period, unit=self.relay_slave_id)
                    # 这里状态变化已经在refresh_relay_status里会被捕获并记录
                    self.log(f"🕒 DO{i + 1} 时间段控制 {'闭合' if in_period else '断开'}")
                except Exception as e:
                    self.log(f"❌ DO{i + 1} 时间段控制异常: {e}")

    # 选择保存目录
    def select_save_directory(self):
        folder = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if folder:
            self.save_path = folder
            self.start_save_btn.setEnabled(True)
            self.log(f"📁 选择保存目录: {folder}")

    # 开始保存数据
    def start_saving(self):
        if not self.save_path:
            QMessageBox.warning(self, "保存目录未选择", "请先选择保存目录！")
            return
        self.save_data = True
        self.start_save_btn.setEnabled(False)
        self.stop_save_btn.setEnabled(True)
        self.log("💾 开始保存温湿度数据")

    # 停止保存数据
    def stop_saving(self):
        self.save_data = False
        self.start_save_btn.setEnabled(True)
        self.stop_save_btn.setEnabled(False)
        self.log("🛑 停止保存温湿度数据")

    # 记录继电器动作日志
    def record_relay_action(self, do_num, action):
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.relay_action_log.append((now, f"DO{do_num}", action))
        self.log(f"📝 记录继电器动作: DO{do_num} {action}")

    # 记录运行状态日志
    def record_run_status(self):
        connected = self.modbus_client is not None and self.modbus_client.connected
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.run_status_log.append((now, connected))
        status_str = "正常" if connected else "异常"
        self.log(f"📊 运行状态记录: {now} - {status_str}")

    # 导出继电器动作日志到CSV
    def export_relay_action_log(self):
        if not self.relay_action_log:
            QMessageBox.information(self, "导出日志", "继电器动作日志为空！")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "保存继电器动作日志", "继电器动作日志.csv", "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            with open(file_path, mode='w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(["时间", "继电器", "动作"])
                writer.writerows(self.relay_action_log)
            self.log(f"✅ 继电器动作日志已导出: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出继电器动作日志失败: {e}")

    # 导出运行状态日志到CSV
    def export_run_status_log(self):
        if not self.run_status_log:
            QMessageBox.information(self, "导出日志", "运行状态日志为空！")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "保存运行状态日志", "运行状态日志.csv", "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            with open(file_path, mode='w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(["时间", "状态"])
                
                for time_str, status in self.run_status_log:
                    writer.writerow([time_str, "正常" if status else "异常"])
            self.log(f"✅ 运行状态日志已导出: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出运行状态日志失败: {e}")

    # 日志输出
    def log(self, msg):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {msg}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())
