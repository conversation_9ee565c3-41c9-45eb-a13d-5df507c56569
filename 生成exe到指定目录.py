#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将自动控制程序打包成exe文件并输出到指定目录
目标目录: D:\kongzhi
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 目标输出目录
TARGET_DIR = r"D:\kongzhi"

def ensure_target_directory():
    """确保目标目录存在"""
    target_path = Path(TARGET_DIR)
    try:
        target_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 目标目录已准备: {TARGET_DIR}")
        return True
    except Exception as e:
        print(f"❌ 创建目标目录失败: {e}")
        return False

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    [r'{os.path.abspath("jidianqikongzhi/自动控制程序.py")}'],
    pathex=[r'{os.path.abspath("jidianqikongzhi")}'],
    binaries=[],
    datas=[
        (r'{os.path.abspath("jidianqikongzhi/使用说明.md")}', '.'),
    ],
    hiddenimports=[
        'serial.tools.list_ports',
        'pymodbus.client.sync',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'csv',
        'datetime',
        'os',
        'shutil',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='自动继电器温湿度监控系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

# 将exe文件输出到指定目录
import shutil
import os
target_dir = r"{TARGET_DIR}"
if not os.path.exists(target_dir):
    os.makedirs(target_dir)

# 复制exe文件到目标目录
exe_source = os.path.join(DISTPATH, '自动继电器温湿度监控系统.exe')
exe_target = os.path.join(target_dir, '自动继电器温湿度监控系统.exe')
'''
    
    spec_file = '自动控制程序_打包.spec'
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✅ 已创建 PyInstaller 配置文件: {spec_file}")
    return spec_file

def build_exe():
    """使用PyInstaller打包exe文件"""
    print("🚀 开始打包exe文件...")
    print("=" * 50)
    
    try:
        # 创建spec文件
        spec_file = create_spec_file()
        
        # 运行PyInstaller
        cmd = ['pyinstaller', '--clean', '--distpath', 'temp_dist', spec_file]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        
        print("✅ PyInstaller 打包成功！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 打包过程中发生错误: {e}")
        return False

def copy_files_to_target():
    """复制文件到目标目录"""
    print(f"\n📦 复制文件到目标目录: {TARGET_DIR}")
    
    # 检查临时dist目录
    temp_dist = Path('temp_dist')
    if not temp_dist.exists():
        print("❌ 临时输出目录不存在")
        return False
    
    exe_file = temp_dist / '自动继电器温湿度监控系统.exe'
    if not exe_file.exists():
        print("❌ 未找到生成的exe文件")
        return False
    
    try:
        target_path = Path(TARGET_DIR)
        
        # 复制exe文件
        target_exe = target_path / '自动继电器温湿度监控系统.exe'
        shutil.copy2(exe_file, target_exe)
        print(f"✅ 已复制exe文件: {target_exe}")
        print(f"📏 文件大小: {target_exe.stat().st_size / (1024*1024):.1f} MB")
        
        # 复制使用说明
        readme_source = Path('jidianqikongzhi/使用说明.md')
        if readme_source.exists():
            readme_target = target_path / '使用说明.md'
            shutil.copy2(readme_source, readme_target)
            print(f"✅ 已复制使用说明: {readme_target}")
        
        # 创建启动批处理文件
        bat_content = '''@echo off
chcp 65001 >nul
echo ========================================
echo    自动继电器和温湿度监控系统
echo ========================================
echo.
echo 正在启动程序...
echo.

start "" "自动继电器温湿度监控系统.exe"
'''
        
        bat_file = target_path / '启动程序.bat'
        with open(bat_file, 'w', encoding='utf-8') as f:
            f.write(bat_content)
        print(f"✅ 已创建启动批处理文件: {bat_file}")
        
        # 创建功能说明文件
        info_content = f'''# 自动继电器温湿度监控系统

## 文件说明
- 自动继电器温湿度监控系统.exe - 主程序
- 启动程序.bat - 启动脚本
- 使用说明.md - 详细使用说明

## 快速开始
1. 双击"启动程序.bat"或直接双击exe文件
2. 程序会自动检测并连接串口
3. 开始实时监控温湿度和继电器状态
4. 数据会自动保存到"数据记录"文件夹

## 主要功能
✅ 自动串口连接
✅ 实时温湿度显示
✅ 继电器控制
✅ 定时数据保存（每10分钟）
✅ 自动创建日期文件夹

## 生成信息
生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
生成位置: {TARGET_DIR}
'''
        
        info_file = target_path / 'README.txt'
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(info_content)
        print(f"✅ 已创建说明文件: {info_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 复制文件失败: {e}")
        return False

def clean_temp_files():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    
    items_to_clean = [
        'temp_dist',
        'build', 
        '__pycache__',
        '自动控制程序_打包.spec'
    ]
    
    for item in items_to_clean:
        item_path = Path(item)
        if item_path.exists():
            try:
                if item_path.is_dir():
                    shutil.rmtree(item_path)
                else:
                    item_path.unlink()
                print(f"✅ 已删除: {item}")
            except Exception as e:
                print(f"⚠️ 删除 {item} 失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("    自动继电器温湿度监控系统 - exe生成工具")
    print(f"    目标目录: {TARGET_DIR}")
    print("=" * 60)
    print()
    
    # 检查当前目录和必要文件
    current_dir = os.getcwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    required_files = [
        'jidianqikongzhi/自动控制程序.py',
        'jidianqikongzhi/使用说明.md'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在正确的目录中运行此脚本")
        input("按回车键退出...")
        return
    
    print("✅ 所有必要文件都存在")
    print()
    
    # 确保目标目录存在
    if not ensure_target_directory():
        input("按回车键退出...")
        return
    
    # 打包exe
    if build_exe():
        if copy_files_to_target():
            print("\n🎉 exe文件生成完成！")
            print("=" * 50)
            print(f"📁 输出目录: {TARGET_DIR}")
            print(f"🚀 可执行文件: {TARGET_DIR}\\自动继电器温湿度监控系统.exe")
            print(f"📖 使用说明: {TARGET_DIR}\\使用说明.md")
            print(f"🔧 启动脚本: {TARGET_DIR}\\启动程序.bat")
            print()
            
            # 询问是否清理临时文件
            choice = input("是否清理临时文件？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是', '']:
                clean_temp_files()
            
            print(f"\n✅ 所有操作完成！exe文件已生成到: {TARGET_DIR}")
            
            # 询问是否打开目标文件夹
            choice = input("是否打开目标文件夹？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是', '']:
                try:
                    os.startfile(TARGET_DIR)
                except Exception as e:
                    print(f"⚠️ 打开文件夹失败: {e}")
        else:
            print("❌ 复制文件到目标目录失败")
    else:
        print("❌ 打包失败，请检查错误信息")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
