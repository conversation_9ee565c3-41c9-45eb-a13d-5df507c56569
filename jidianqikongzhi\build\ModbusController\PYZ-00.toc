('D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\ModbusController\\PYZ-00.pyz',
 [('PyQt5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\calendar.py', 'PYMODULE'),
  ('configparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('datetime', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dis.py', 'PYMODULE'),
  ('email',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fractions.py',
   'PYMODULE'),
  ('getopt', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hashlib.py', 'PYMODULE'),
  ('imp', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ipaddress.py',
   'PYMODULE'),
  ('logging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\py_compile.py',
   'PYMODULE'),
  ('pymodbus',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\__init__.py',
   'PYMODULE'),
  ('pymodbus.bit_read_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\bit_read_message.py',
   'PYMODULE'),
  ('pymodbus.bit_write_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\bit_write_message.py',
   'PYMODULE'),
  ('pymodbus.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\__init__.py',
   'PYMODULE'),
  ('pymodbus.client.common',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\common.py',
   'PYMODULE'),
  ('pymodbus.client.sync',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\sync.py',
   'PYMODULE'),
  ('pymodbus.compat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\compat.py',
   'PYMODULE'),
  ('pymodbus.constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\constants.py',
   'PYMODULE'),
  ('pymodbus.device',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\device.py',
   'PYMODULE'),
  ('pymodbus.diag_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\diag_message.py',
   'PYMODULE'),
  ('pymodbus.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\exceptions.py',
   'PYMODULE'),
  ('pymodbus.factory',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\factory.py',
   'PYMODULE'),
  ('pymodbus.file_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\file_message.py',
   'PYMODULE'),
  ('pymodbus.framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\__init__.py',
   'PYMODULE'),
  ('pymodbus.framer.ascii_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\ascii_framer.py',
   'PYMODULE'),
  ('pymodbus.framer.binary_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\binary_framer.py',
   'PYMODULE'),
  ('pymodbus.framer.rtu_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\rtu_framer.py',
   'PYMODULE'),
  ('pymodbus.framer.socket_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\socket_framer.py',
   'PYMODULE'),
  ('pymodbus.framer.tls_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\tls_framer.py',
   'PYMODULE'),
  ('pymodbus.interfaces',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\interfaces.py',
   'PYMODULE'),
  ('pymodbus.mei_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\mei_message.py',
   'PYMODULE'),
  ('pymodbus.other_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\other_message.py',
   'PYMODULE'),
  ('pymodbus.pdu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu.py',
   'PYMODULE'),
  ('pymodbus.register_read_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\register_read_message.py',
   'PYMODULE'),
  ('pymodbus.register_write_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\register_write_message.py',
   'PYMODULE'),
  ('pymodbus.transaction',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\transaction.py',
   'PYMODULE'),
  ('pymodbus.utilities',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\utilities.py',
   'PYMODULE'),
  ('pymodbus.version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\version.py',
   'PYMODULE'),
  ('quopri', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\random.py', 'PYMODULE'),
  ('selectors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\selectors.py',
   'PYMODULE'),
  ('serial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialjava',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('shutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\signal.py', 'PYMODULE'),
  ('six',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tarfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\textwrap.py', 'PYMODULE'),
  ('threading',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\threading.py',
   'PYMODULE'),
  ('token', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('uu', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipimport.py',
   'PYMODULE')])
