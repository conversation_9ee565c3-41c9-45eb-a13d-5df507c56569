import os
import re
import pandas as pd

# 根目录路径
root_dir = r'F:/daman/0709'

# 遍历所有以 scan_0630_ 开头的文件夹
for scan_folder in os.listdir(root_dir):
    if scan_folder.startswith('scan_0709_'):
        scan_path = os.path.join(root_dir, scan_folder)

        if not os.path.isdir(scan_path):
            continue  # 跳过非文件夹

        angle_data = []

        # 遍历子文件夹 0, 1, 2, ...
        for subfolder in sorted(os.listdir(scan_path), key=lambda x: int(x) if x.isdigit() else -1):
            subfolder_path = os.path.join(scan_path, subfolder)
            params_file = os.path.join(subfolder_path, 'params')

            if os.path.isdir(subfolder_path) and os.path.isfile(params_file):
                try:
                    with open(params_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 提取 H 和 V 数值
                    h_match = re.search(r'H:\s*(-?\d+\.?\d*)', content)
                    v_match = re.search(r'V:\s*(-?\d+\.?\d*)', content)

                    if h_match and v_match:
                        h_val = float(h_match.group(1))
                        v_val = float(v_match.group(1))
                        angle_data.append({
                            'Folder': subfolder,
                            'H': h_val,
                            'V': v_val
                        })
                    else:
                        print(f'⚠️ 未找到 H/V 参数：{params_file}')
                except Exception as e:
                    print(f'⚠️ 读取失败：{params_file}，错误：{e}')

        # 保存当前scan文件夹的结果为Excel
        if angle_data:
            df = pd.DataFrame(angle_data)
            output_excel = os.path.join(scan_path, 'angle_params.xlsx')
            df.to_excel(output_excel, index=False)
            print(f'✅ 保存成功：{output_excel}')
        else:
            print(f'⚠️ 无有效参数数据：{scan_path}')
