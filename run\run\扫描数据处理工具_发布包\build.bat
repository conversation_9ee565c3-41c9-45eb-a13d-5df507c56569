@echo off
echo =================================
echo 扫描数据处理工具 - 自动打包脚本
echo =================================
echo.

echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo.
echo 正在安装依赖包...
pip install pyinstaller pillow

echo.
echo 正在打包exe文件...
pyinstaller --onefile --windowed --name="扫描数据处理工具" scan_processor_gui.py

echo.
if exist "dist\扫描数据处理工具.exe" (
    echo ✓ 打包成功！
    echo exe文件位置: dist\扫描数据处理工具.exe
    echo.
    echo 是否打开dist文件夹？
    set /p choice="输入 y 打开文件夹，或按任意键退出: "
    if /i "%choice%"=="y" (
        explorer dist
    )
) else (
    echo ✗ 打包失败，请检查错误信息
)

echo.
pause
