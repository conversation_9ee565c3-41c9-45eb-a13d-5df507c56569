('D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\tiaoshi9.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('v', None, 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('tiaoshi9',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\tiaoshi9.py',
   'PYSOURCE'),
  ('python39.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python39.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\sip.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\MSVCP140.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libffi-7.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('zipfile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\zipfile.pyc',
   'DATA'),
  ('argparse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\argparse.pyc',
   'DATA'),
  ('textwrap.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\textwrap.pyc',
   'DATA'),
  ('copy.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\copy.pyc',
   'DATA'),
  ('gettext.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\gettext.pyc',
   'DATA'),
  ('py_compile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\py_compile.pyc',
   'DATA'),
  ('importlib\\machinery.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\machinery.pyc',
   'DATA'),
  ('importlib\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\__init__.pyc',
   'DATA'),
  ('importlib\\_bootstrap.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\_bootstrap.pyc',
   'DATA'),
  ('importlib\\_bootstrap_external.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\_bootstrap_external.pyc',
   'DATA'),
  ('importlib\\metadata.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\metadata.pyc',
   'DATA'),
  ('importlib\\abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\abc.pyc',
   'DATA'),
  ('typing.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\typing.pyc',
   'DATA'),
  ('configparser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\configparser.pyc',
   'DATA'),
  ('pathlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pathlib.pyc',
   'DATA'),
  ('urllib\\parse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\urllib\\parse.pyc',
   'DATA'),
  ('urllib\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\urllib\\__init__.pyc',
   'DATA'),
  ('ipaddress.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ipaddress.pyc',
   'DATA'),
  ('fnmatch.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\fnmatch.pyc',
   'DATA'),
  ('email\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\__init__.pyc',
   'DATA'),
  ('email\\parser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\parser.pyc',
   'DATA'),
  ('email\\_policybase.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_policybase.pyc',
   'DATA'),
  ('email\\utils.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\utils.pyc',
   'DATA'),
  ('email\\_parseaddr.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_parseaddr.pyc',
   'DATA'),
  ('calendar.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\calendar.pyc',
   'DATA'),
  ('socket.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\socket.pyc',
   'DATA'),
  ('selectors.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\selectors.pyc',
   'DATA'),
  ('random.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\random.pyc',
   'DATA'),
  ('statistics.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\statistics.pyc',
   'DATA'),
  ('decimal.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\decimal.pyc',
   'DATA'),
  ('_pydecimal.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_pydecimal.pyc',
   'DATA'),
  ('contextvars.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\contextvars.pyc',
   'DATA'),
  ('fractions.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\fractions.pyc',
   'DATA'),
  ('numbers.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\numbers.pyc',
   'DATA'),
  ('hashlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\hashlib.pyc',
   'DATA'),
  ('logging\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\logging\\__init__.pyc',
   'DATA'),
  ('pickle.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pickle.pyc',
   'DATA'),
  ('pprint.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pprint.pyc',
   'DATA'),
  ('_compat_pickle.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_compat_pickle.pyc',
   'DATA'),
  ('string.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\string.pyc',
   'DATA'),
  ('bisect.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\bisect.pyc',
   'DATA'),
  ('email\\feedparser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\feedparser.pyc',
   'DATA'),
  ('email\\message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\message.pyc',
   'DATA'),
  ('email\\policy.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\policy.pyc',
   'DATA'),
  ('email\\contentmanager.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\contentmanager.pyc',
   'DATA'),
  ('email\\quoprimime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\quoprimime.pyc',
   'DATA'),
  ('email\\headerregistry.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\headerregistry.pyc',
   'DATA'),
  ('email\\iterators.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\iterators.pyc',
   'DATA'),
  ('email\\generator.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\generator.pyc',
   'DATA'),
  ('email\\_encoded_words.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_encoded_words.pyc',
   'DATA'),
  ('base64.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\base64.pyc',
   'DATA'),
  ('getopt.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\getopt.pyc',
   'DATA'),
  ('quopri.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\quopri.pyc',
   'DATA'),
  ('uu.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\uu.pyc',
   'DATA'),
  ('optparse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\optparse.pyc',
   'DATA'),
  ('email\\_header_value_parser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_header_value_parser.pyc',
   'DATA'),
  ('email\\header.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\header.pyc',
   'DATA'),
  ('email\\base64mime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\base64mime.pyc',
   'DATA'),
  ('email\\charset.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\charset.pyc',
   'DATA'),
  ('email\\encoders.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\encoders.pyc',
   'DATA'),
  ('email\\errors.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\errors.pyc',
   'DATA'),
  ('tokenize.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\tokenize.pyc',
   'DATA'),
  ('token.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\token.pyc',
   'DATA'),
  ('lzma.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\lzma.pyc',
   'DATA'),
  ('_compression.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_compression.pyc',
   'DATA'),
  ('bz2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\bz2.pyc',
   'DATA'),
  ('contextlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\contextlib.pyc',
   'DATA'),
  ('_strptime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_strptime.pyc',
   'DATA'),
  ('threading.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\threading.pyc',
   'DATA'),
  ('_threading_local.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_threading_local.pyc',
   'DATA'),
  ('struct.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\struct.pyc',
   'DATA'),
  ('shutil.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\shutil.pyc',
   'DATA'),
  ('tarfile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\tarfile.pyc',
   'DATA'),
  ('gzip.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\gzip.pyc',
   'DATA'),
  ('importlib\\util.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\util.pyc',
   'DATA'),
  ('inspect.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\inspect.pyc',
   'DATA'),
  ('dis.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\dis.pyc',
   'DATA'),
  ('opcode.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\opcode.pyc',
   'DATA'),
  ('ast.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ast.pyc',
   'DATA'),
  ('pkgutil.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pkgutil.pyc',
   'DATA'),
  ('zipimport.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\zipimport.pyc',
   'DATA'),
  ('_pyi_rth_utils\\qt.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_pyi_rth_utils\\qt.pyc',
   'DATA'),
  ('_pyi_rth_utils\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_pyi_rth_utils\\__init__.pyc',
   'DATA'),
  ('abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\abc.pyc',
   'DATA'),
  ('_py_abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_py_abc.pyc',
   'DATA'),
  ('reprlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\reprlib.pyc',
   'DATA'),
  ('sre_constants.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\sre_constants.pyc',
   'DATA'),
  ('stat.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\stat.pyc',
   'DATA'),
  ('posixpath.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\posixpath.pyc',
   'DATA'),
  ('os.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\os.pyc',
   'DATA'),
  ('subprocess.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\subprocess.pyc',
   'DATA'),
  ('signal.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\signal.pyc',
   'DATA'),
  ('codecs.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\codecs.pyc',
   'DATA'),
  ('_weakrefset.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_weakrefset.pyc',
   'DATA'),
  ('warnings.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\warnings.pyc',
   'DATA'),
  ('tracemalloc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\tracemalloc.pyc',
   'DATA'),
  ('linecache.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\linecache.pyc',
   'DATA'),
  ('genericpath.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\genericpath.pyc',
   'DATA'),
  ('heapq.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\heapq.pyc',
   'DATA'),
  ('traceback.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\traceback.pyc',
   'DATA'),
  ('sre_parse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\sre_parse.pyc',
   'DATA'),
  ('types.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\types.pyc',
   'DATA'),
  ('_bootlocale.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_bootlocale.pyc',
   'DATA'),
  ('keyword.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\keyword.pyc',
   'DATA'),
  ('enum.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\enum.pyc',
   'DATA'),
  ('weakref.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\weakref.pyc',
   'DATA'),
  ('sre_compile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\sre_compile.pyc',
   'DATA'),
  ('functools.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\functools.pyc',
   'DATA'),
  ('re.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\re.pyc',
   'DATA'),
  ('locale.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\locale.pyc',
   'DATA'),
  ('operator.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\operator.pyc',
   'DATA'),
  ('ntpath.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ntpath.pyc',
   'DATA'),
  ('copyreg.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\copyreg.pyc',
   'DATA'),
  ('collections\\abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\collections\\abc.pyc',
   'DATA'),
  ('collections\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\collections\\__init__.pyc',
   'DATA'),
  ('encodings\\zlib_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\zlib_codec.pyc',
   'DATA'),
  ('encodings\\uu_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\uu_codec.pyc',
   'DATA'),
  ('encodings\\utf_8_sig.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_8_sig.pyc',
   'DATA'),
  ('encodings\\utf_8.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_8.pyc',
   'DATA'),
  ('encodings\\utf_7.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_7.pyc',
   'DATA'),
  ('encodings\\utf_32_le.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_32_le.pyc',
   'DATA'),
  ('encodings\\utf_32_be.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_32_be.pyc',
   'DATA'),
  ('encodings\\utf_32.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_32.pyc',
   'DATA'),
  ('encodings\\utf_16_le.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_16_le.pyc',
   'DATA'),
  ('encodings\\utf_16_be.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_16_be.pyc',
   'DATA'),
  ('encodings\\utf_16.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_16.pyc',
   'DATA'),
  ('encodings\\unicode_escape.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\unicode_escape.pyc',
   'DATA'),
  ('encodings\\undefined.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\undefined.pyc',
   'DATA'),
  ('encodings\\tis_620.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\tis_620.pyc',
   'DATA'),
  ('encodings\\shift_jisx0213.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\shift_jisx0213.pyc',
   'DATA'),
  ('encodings\\shift_jis_2004.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\shift_jis_2004.pyc',
   'DATA'),
  ('encodings\\shift_jis.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\shift_jis.pyc',
   'DATA'),
  ('encodings\\rot_13.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\rot_13.pyc',
   'DATA'),
  ('encodings\\raw_unicode_escape.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\raw_unicode_escape.pyc',
   'DATA'),
  ('encodings\\quopri_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\quopri_codec.pyc',
   'DATA'),
  ('encodings\\punycode.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\punycode.pyc',
   'DATA'),
  ('encodings\\ptcp154.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\ptcp154.pyc',
   'DATA'),
  ('encodings\\palmos.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\palmos.pyc',
   'DATA'),
  ('encodings\\oem.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\oem.pyc',
   'DATA'),
  ('encodings\\mbcs.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mbcs.pyc',
   'DATA'),
  ('encodings\\mac_turkish.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_turkish.pyc',
   'DATA'),
  ('encodings\\mac_romanian.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_romanian.pyc',
   'DATA'),
  ('encodings\\mac_roman.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_roman.pyc',
   'DATA'),
  ('encodings\\mac_latin2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_latin2.pyc',
   'DATA'),
  ('encodings\\mac_iceland.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_iceland.pyc',
   'DATA'),
  ('encodings\\mac_greek.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_greek.pyc',
   'DATA'),
  ('encodings\\mac_farsi.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_farsi.pyc',
   'DATA'),
  ('encodings\\mac_cyrillic.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_cyrillic.pyc',
   'DATA'),
  ('encodings\\mac_croatian.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_croatian.pyc',
   'DATA'),
  ('encodings\\mac_arabic.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_arabic.pyc',
   'DATA'),
  ('encodings\\latin_1.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\latin_1.pyc',
   'DATA'),
  ('encodings\\kz1048.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\kz1048.pyc',
   'DATA'),
  ('encodings\\koi8_u.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\koi8_u.pyc',
   'DATA'),
  ('encodings\\koi8_t.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\koi8_t.pyc',
   'DATA'),
  ('encodings\\koi8_r.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\koi8_r.pyc',
   'DATA'),
  ('encodings\\johab.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\johab.pyc',
   'DATA'),
  ('encodings\\iso8859_9.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_9.pyc',
   'DATA'),
  ('encodings\\iso8859_8.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_8.pyc',
   'DATA'),
  ('encodings\\iso8859_7.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_7.pyc',
   'DATA'),
  ('encodings\\iso8859_6.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_6.pyc',
   'DATA'),
  ('encodings\\iso8859_5.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_5.pyc',
   'DATA'),
  ('encodings\\iso8859_4.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_4.pyc',
   'DATA'),
  ('encodings\\iso8859_3.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_3.pyc',
   'DATA'),
  ('encodings\\iso8859_2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_2.pyc',
   'DATA'),
  ('encodings\\iso8859_16.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_16.pyc',
   'DATA'),
  ('encodings\\iso8859_15.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_15.pyc',
   'DATA'),
  ('encodings\\iso8859_14.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_14.pyc',
   'DATA'),
  ('encodings\\iso8859_13.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_13.pyc',
   'DATA'),
  ('encodings\\iso8859_11.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_11.pyc',
   'DATA'),
  ('encodings\\iso8859_10.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_10.pyc',
   'DATA'),
  ('encodings\\iso8859_1.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_1.pyc',
   'DATA'),
  ('encodings\\iso2022_kr.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_kr.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_ext.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_ext.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_3.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_3.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_2004.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_2004.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_2.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_1.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_1.pyc',
   'DATA'),
  ('encodings\\iso2022_jp.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp.pyc',
   'DATA'),
  ('encodings\\idna.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\idna.pyc',
   'DATA'),
  ('stringprep.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\stringprep.pyc',
   'DATA'),
  ('encodings\\hz.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\hz.pyc',
   'DATA'),
  ('encodings\\hp_roman8.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\hp_roman8.pyc',
   'DATA'),
  ('encodings\\hex_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\hex_codec.pyc',
   'DATA'),
  ('encodings\\gbk.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\gbk.pyc',
   'DATA'),
  ('encodings\\gb2312.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\gb2312.pyc',
   'DATA'),
  ('encodings\\gb18030.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\gb18030.pyc',
   'DATA'),
  ('encodings\\euc_kr.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_kr.pyc',
   'DATA'),
  ('encodings\\euc_jp.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_jp.pyc',
   'DATA'),
  ('encodings\\euc_jisx0213.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_jisx0213.pyc',
   'DATA'),
  ('encodings\\euc_jis_2004.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_jis_2004.pyc',
   'DATA'),
  ('encodings\\cp950.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp950.pyc',
   'DATA'),
  ('encodings\\cp949.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp949.pyc',
   'DATA'),
  ('encodings\\cp932.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp932.pyc',
   'DATA'),
  ('encodings\\cp875.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp875.pyc',
   'DATA'),
  ('encodings\\cp874.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp874.pyc',
   'DATA'),
  ('encodings\\cp869.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp869.pyc',
   'DATA'),
  ('encodings\\cp866.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp866.pyc',
   'DATA'),
  ('encodings\\cp865.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp865.pyc',
   'DATA'),
  ('encodings\\cp864.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp864.pyc',
   'DATA'),
  ('encodings\\cp863.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp863.pyc',
   'DATA'),
  ('encodings\\cp862.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp862.pyc',
   'DATA'),
  ('encodings\\cp861.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp861.pyc',
   'DATA'),
  ('encodings\\cp860.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp860.pyc',
   'DATA'),
  ('encodings\\cp858.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp858.pyc',
   'DATA'),
  ('encodings\\cp857.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp857.pyc',
   'DATA'),
  ('encodings\\cp856.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp856.pyc',
   'DATA'),
  ('encodings\\cp855.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp855.pyc',
   'DATA'),
  ('encodings\\cp852.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp852.pyc',
   'DATA'),
  ('encodings\\cp850.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp850.pyc',
   'DATA'),
  ('encodings\\cp775.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp775.pyc',
   'DATA'),
  ('encodings\\cp737.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp737.pyc',
   'DATA'),
  ('encodings\\cp720.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp720.pyc',
   'DATA'),
  ('encodings\\cp500.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp500.pyc',
   'DATA'),
  ('encodings\\cp437.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp437.pyc',
   'DATA'),
  ('encodings\\cp424.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp424.pyc',
   'DATA'),
  ('encodings\\cp273.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp273.pyc',
   'DATA'),
  ('encodings\\cp1258.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1258.pyc',
   'DATA'),
  ('encodings\\cp1257.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1257.pyc',
   'DATA'),
  ('encodings\\cp1256.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1256.pyc',
   'DATA'),
  ('encodings\\cp1255.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1255.pyc',
   'DATA'),
  ('encodings\\cp1254.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1254.pyc',
   'DATA'),
  ('encodings\\cp1253.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1253.pyc',
   'DATA'),
  ('encodings\\cp1252.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1252.pyc',
   'DATA'),
  ('encodings\\cp1251.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1251.pyc',
   'DATA'),
  ('encodings\\cp1250.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1250.pyc',
   'DATA'),
  ('encodings\\cp1140.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1140.pyc',
   'DATA'),
  ('encodings\\cp1125.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1125.pyc',
   'DATA'),
  ('encodings\\cp1026.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1026.pyc',
   'DATA'),
  ('encodings\\cp1006.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1006.pyc',
   'DATA'),
  ('encodings\\cp037.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp037.pyc',
   'DATA'),
  ('encodings\\charmap.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\charmap.pyc',
   'DATA'),
  ('encodings\\bz2_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\bz2_codec.pyc',
   'DATA'),
  ('encodings\\big5hkscs.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\big5hkscs.pyc',
   'DATA'),
  ('encodings\\big5.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\big5.pyc',
   'DATA'),
  ('encodings\\base64_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\base64_codec.pyc',
   'DATA'),
  ('encodings\\ascii.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\ascii.pyc',
   'DATA'),
  ('encodings\\aliases.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\aliases.pyc',
   'DATA'),
  ('encodings\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\__init__.pyc',
   'DATA'),
  ('_collections_abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_collections_abc.pyc',
   'DATA'),
  ('io.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\io.pyc',
   'DATA'),
  ('pymodbus\\client\\sync.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\client\\sync.pyc',
   'DATA'),
  ('pymodbus\\client\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\client\\__init__.pyc',
   'DATA'),
  ('pymodbus\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\__init__.pyc',
   'DATA'),
  ('pymodbus\\version.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\version.pyc',
   'DATA'),
  ('pymodbus\\client\\common.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\client\\common.pyc',
   'DATA'),
  ('pymodbus\\other_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\other_message.pyc',
   'DATA'),
  ('pymodbus\\compat.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\compat.pyc',
   'DATA'),
  ('imp.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\imp.pyc',
   'DATA'),
  ('socketserver.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\socketserver.pyc',
   'DATA'),
  ('six.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\six.pyc',
   'DATA'),
  ('__future__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\__future__.pyc',
   'DATA'),
  ('pymodbus\\device.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\device.pyc',
   'DATA'),
  ('pymodbus\\interfaces.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\interfaces.pyc',
   'DATA'),
  ('pymodbus\\pdu.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\pdu.pyc',
   'DATA'),
  ('pymodbus\\file_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\file_message.pyc',
   'DATA'),
  ('pymodbus\\diag_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\diag_message.pyc',
   'DATA'),
  ('pymodbus\\register_write_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\register_write_message.pyc',
   'DATA'),
  ('pymodbus\\register_read_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\register_read_message.pyc',
   'DATA'),
  ('pymodbus\\bit_write_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\bit_write_message.pyc',
   'DATA'),
  ('pymodbus\\bit_read_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\bit_read_message.pyc',
   'DATA'),
  ('pymodbus\\transaction.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\transaction.pyc',
   'DATA'),
  ('pymodbus\\framer\\binary_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\binary_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\__init__.pyc',
   'DATA'),
  ('pymodbus\\framer\\tls_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\tls_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\socket_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\socket_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\rtu_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\rtu_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\ascii_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\ascii_framer.pyc',
   'DATA'),
  ('pymodbus\\exceptions.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\exceptions.pyc',
   'DATA'),
  ('pymodbus\\factory.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\factory.pyc',
   'DATA'),
  ('pymodbus\\mei_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\mei_message.pyc',
   'DATA'),
  ('pymodbus\\utilities.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\utilities.pyc',
   'DATA'),
  ('pymodbus\\constants.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\constants.pyc',
   'DATA'),
  ('ssl.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ssl.pyc',
   'DATA'),
  ('serial\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\__init__.pyc',
   'DATA'),
  ('serial\\serialjava.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialjava.pyc',
   'DATA'),
  ('serial\\serialposix.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialposix.pyc',
   'DATA'),
  ('serial\\serialwin32.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialwin32.pyc',
   'DATA'),
  ('ctypes\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ctypes\\__init__.pyc',
   'DATA'),
  ('ctypes\\_endian.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ctypes\\_endian.pyc',
   'DATA'),
  ('serial\\win32.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\win32.pyc',
   'DATA'),
  ('ctypes\\wintypes.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ctypes\\wintypes.pyc',
   'DATA'),
  ('serial\\serialcli.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialcli.pyc',
   'DATA'),
  ('serial\\serialutil.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialutil.pyc',
   'DATA'),
  ('PyQt5\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\PyQt5\\__init__.pyc',
   'DATA'),
  ('datetime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\datetime.pyc',
   'DATA'),
  ('csv.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\csv.pyc',
   'DATA')],
 'python39.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
