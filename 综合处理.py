import os
import shutil
from PIL import Image

# ------------------路径和参数设置------------------

# 根目录路径
source_root_dir = r'E:/scan_1029_094848'  # 样本根目录
grouped_dir = r'E:/scan_1029_094848/分组样带'  # 分组后的目标目录
destination_dir = r'E:/scan_1029_094848/提取图片'  # 提取图片保存的目标目录
cropped_output_dir = r'E:/scan_1029_094848/图片裁剪结果'  # 裁剪结果保存目录

# 分组参数
folders_per_group = 10  # 每组的文件夹数量

# 裁剪参数
width_crop = 1608  # 水平方向保留的像素宽度

# ------------------步骤 1：将文件夹分成样带并打印分组细节------------------

# 创建分组目录
os.makedirs(grouped_dir, exist_ok=True)

# # 获取文件夹列表并按数字顺序排序
# folders = sorted([f for f in os.listdir(source_root_dir) if os.path.isdir(os.path.join(source_root_dir, f))],
#                  key=lambda x: int(x))
# 获取文件夹列表并按数字顺序排序，只包含能够转换为数字的文件夹
folders = sorted([f for f in os.listdir(source_root_dir)
                  if os.path.isdir(os.path.join(source_root_dir, f)) and f.isdigit()],
                 key=lambda x: int(x))

# 将文件夹分成指定数量的样带，并打印分组信息
for i in range(0, len(folders), folders_per_group):
    group_number = i // folders_per_group + 1
    group_name = f'样带{group_number}'
    group_path = os.path.join(grouped_dir, group_name)

    # 创建样带文件夹
    os.makedirs(group_path, exist_ok=True)
    print(f'\n创建 {group_name} 包含以下文件夹:')

    # 将文件夹复制到分组文件夹中并打印包含的文件夹
    for folder in folders[i:i + folders_per_group]:
        print(f'  - {folder} 已复制到 {group_name}')
        shutil.copytree(os.path.join(source_root_dir, folder), os.path.join(group_path, folder))

print("文件夹分组完成\n")

# ------------------步骤 2：提取图片并重命名保存------------------

# 创建提取图片保存目录
os.makedirs(destination_dir, exist_ok=True)
image_counter = 1  # 初始化图片计数器

# 从每个分组中提取图片
for group_folder in os.listdir(grouped_dir):
    group_path = os.path.join(grouped_dir, group_folder)

    if os.path.isdir(group_path):
        for sub_folder in os.listdir(group_path):
            sub_folder_path = os.path.join(group_path, sub_folder)

            if os.path.isdir(sub_folder_path):
                # 构建 "40.00 ns\res_bmp" 路径
                res_bmp_path = os.path.join(sub_folder_path, '37.00 ns', 'res_bmp')

                # 检查 res_bmp 文件夹是否存在
                if os.path.exists(res_bmp_path):
                    # 获取 res_bmp 文件夹中的所有图片文件，并按名称排序
                    images = sorted([f for f in os.listdir(res_bmp_path) if f.endswith(('.bmp', '.png', '.jpg'))])

                    if images:
                        # 选择第一张图片
                        first_image = images[1]  # 若要选择第一张图片，可改为 images[0]
                        source_image_path = os.path.join(res_bmp_path, first_image)

                        # 目标路径，命名为 "1.bmp", "2.bmp", 依次类推
                        destination_image_path = os.path.join(destination_dir, f'{image_counter}.bmp')

                        # 复制并重命名图片
                        shutil.copyfile(source_image_path, destination_image_path)

                        print(f"已提取: {source_image_path} -> 保存为: {destination_image_path}")

                        # 递增图片计数器
                        image_counter += 1

print("\n图片提取并保存完成\n")

# ------------------步骤 3：裁剪提取的图片------------------

# 创建裁剪结果保存目录
os.makedirs(cropped_output_dir, exist_ok=True)

# 获取目标目录中所有 .bmp 文件，并按文件名从小到大排序
bmp_files = sorted([f for f in os.listdir(destination_dir) if f.endswith('.bmp')], key=lambda x: int(x.split('.')[0]))

# 循环处理每个 .bmp 文件
for img_file in bmp_files:
    img_path = os.path.join(destination_dir, img_file)
    img = Image.open(img_path)

    # 裁剪图片右边，保留 width_crop 个像素宽度
    cropped_img = img.crop((0, 0, width_crop, img.height))

    # 生成裁剪后图片的输出路径
    output_image_path = os.path.join(cropped_output_dir, img_file)

    # 保存裁剪后的图像
    cropped_img.save(output_image_path)

    print(f"{img_file} 已裁剪并保存至 {output_image_path}")

print("\n所有图片裁剪并保存完成")
