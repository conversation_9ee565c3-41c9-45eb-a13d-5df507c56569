(['D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\tiaoshi9.py'],
 ['D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi'],
 [],
 [('D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 True,
 {},
 0,
 [],
 [],
 '3.9.21 (main, Dec 11 2024, 16:35:24) [MSC v.1929 64 bit (AMD64)]',
 [('pyi_rth_pyqt5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('tiaoshi9',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\tiaoshi9.py',
   'PYSOURCE')],
 [],
 [('python39.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python39.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\sip.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\MSVCP140.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libffi-7.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('zipfile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\zipfile.pyc',
   'DATA'),
  ('argparse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\argparse.pyc',
   'DATA'),
  ('textwrap.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\textwrap.pyc',
   'DATA'),
  ('copy.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\copy.pyc',
   'DATA'),
  ('gettext.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\gettext.pyc',
   'DATA'),
  ('py_compile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\py_compile.pyc',
   'DATA'),
  ('importlib\\machinery.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\machinery.pyc',
   'DATA'),
  ('importlib\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\__init__.pyc',
   'DATA'),
  ('importlib\\_bootstrap.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\_bootstrap.pyc',
   'DATA'),
  ('importlib\\_bootstrap_external.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\_bootstrap_external.pyc',
   'DATA'),
  ('importlib\\metadata.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\metadata.pyc',
   'DATA'),
  ('importlib\\abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\abc.pyc',
   'DATA'),
  ('typing.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\typing.pyc',
   'DATA'),
  ('configparser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\configparser.pyc',
   'DATA'),
  ('pathlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pathlib.pyc',
   'DATA'),
  ('urllib\\parse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\urllib\\parse.pyc',
   'DATA'),
  ('urllib\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\urllib\\__init__.pyc',
   'DATA'),
  ('ipaddress.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ipaddress.pyc',
   'DATA'),
  ('fnmatch.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\fnmatch.pyc',
   'DATA'),
  ('email\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\__init__.pyc',
   'DATA'),
  ('email\\parser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\parser.pyc',
   'DATA'),
  ('email\\_policybase.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_policybase.pyc',
   'DATA'),
  ('email\\utils.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\utils.pyc',
   'DATA'),
  ('email\\_parseaddr.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_parseaddr.pyc',
   'DATA'),
  ('calendar.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\calendar.pyc',
   'DATA'),
  ('socket.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\socket.pyc',
   'DATA'),
  ('selectors.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\selectors.pyc',
   'DATA'),
  ('random.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\random.pyc',
   'DATA'),
  ('statistics.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\statistics.pyc',
   'DATA'),
  ('decimal.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\decimal.pyc',
   'DATA'),
  ('_pydecimal.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_pydecimal.pyc',
   'DATA'),
  ('contextvars.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\contextvars.pyc',
   'DATA'),
  ('fractions.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\fractions.pyc',
   'DATA'),
  ('numbers.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\numbers.pyc',
   'DATA'),
  ('hashlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\hashlib.pyc',
   'DATA'),
  ('logging\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\logging\\__init__.pyc',
   'DATA'),
  ('pickle.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pickle.pyc',
   'DATA'),
  ('pprint.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pprint.pyc',
   'DATA'),
  ('_compat_pickle.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_compat_pickle.pyc',
   'DATA'),
  ('string.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\string.pyc',
   'DATA'),
  ('bisect.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\bisect.pyc',
   'DATA'),
  ('email\\feedparser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\feedparser.pyc',
   'DATA'),
  ('email\\message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\message.pyc',
   'DATA'),
  ('email\\policy.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\policy.pyc',
   'DATA'),
  ('email\\contentmanager.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\contentmanager.pyc',
   'DATA'),
  ('email\\quoprimime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\quoprimime.pyc',
   'DATA'),
  ('email\\headerregistry.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\headerregistry.pyc',
   'DATA'),
  ('email\\iterators.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\iterators.pyc',
   'DATA'),
  ('email\\generator.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\generator.pyc',
   'DATA'),
  ('email\\_encoded_words.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_encoded_words.pyc',
   'DATA'),
  ('base64.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\base64.pyc',
   'DATA'),
  ('getopt.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\getopt.pyc',
   'DATA'),
  ('quopri.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\quopri.pyc',
   'DATA'),
  ('uu.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\uu.pyc',
   'DATA'),
  ('optparse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\optparse.pyc',
   'DATA'),
  ('email\\_header_value_parser.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\_header_value_parser.pyc',
   'DATA'),
  ('email\\header.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\header.pyc',
   'DATA'),
  ('email\\base64mime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\base64mime.pyc',
   'DATA'),
  ('email\\charset.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\charset.pyc',
   'DATA'),
  ('email\\encoders.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\encoders.pyc',
   'DATA'),
  ('email\\errors.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\email\\errors.pyc',
   'DATA'),
  ('tokenize.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\tokenize.pyc',
   'DATA'),
  ('token.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\token.pyc',
   'DATA'),
  ('lzma.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\lzma.pyc',
   'DATA'),
  ('_compression.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_compression.pyc',
   'DATA'),
  ('bz2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\bz2.pyc',
   'DATA'),
  ('contextlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\contextlib.pyc',
   'DATA'),
  ('_strptime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_strptime.pyc',
   'DATA'),
  ('threading.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\threading.pyc',
   'DATA'),
  ('_threading_local.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_threading_local.pyc',
   'DATA'),
  ('struct.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\struct.pyc',
   'DATA'),
  ('shutil.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\shutil.pyc',
   'DATA'),
  ('tarfile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\tarfile.pyc',
   'DATA'),
  ('gzip.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\gzip.pyc',
   'DATA'),
  ('importlib\\util.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\importlib\\util.pyc',
   'DATA'),
  ('inspect.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\inspect.pyc',
   'DATA'),
  ('dis.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\dis.pyc',
   'DATA'),
  ('opcode.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\opcode.pyc',
   'DATA'),
  ('ast.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ast.pyc',
   'DATA'),
  ('pkgutil.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pkgutil.pyc',
   'DATA'),
  ('zipimport.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\zipimport.pyc',
   'DATA'),
  ('_pyi_rth_utils\\qt.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_pyi_rth_utils\\qt.pyc',
   'DATA'),
  ('_pyi_rth_utils\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_pyi_rth_utils\\__init__.pyc',
   'DATA'),
  ('abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\abc.pyc',
   'DATA'),
  ('_py_abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_py_abc.pyc',
   'DATA'),
  ('reprlib.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\reprlib.pyc',
   'DATA'),
  ('sre_constants.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\sre_constants.pyc',
   'DATA'),
  ('stat.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\stat.pyc',
   'DATA'),
  ('posixpath.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\posixpath.pyc',
   'DATA'),
  ('os.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\os.pyc',
   'DATA'),
  ('subprocess.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\subprocess.pyc',
   'DATA'),
  ('signal.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\signal.pyc',
   'DATA'),
  ('codecs.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\codecs.pyc',
   'DATA'),
  ('_weakrefset.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_weakrefset.pyc',
   'DATA'),
  ('warnings.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\warnings.pyc',
   'DATA'),
  ('tracemalloc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\tracemalloc.pyc',
   'DATA'),
  ('linecache.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\linecache.pyc',
   'DATA'),
  ('genericpath.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\genericpath.pyc',
   'DATA'),
  ('heapq.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\heapq.pyc',
   'DATA'),
  ('traceback.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\traceback.pyc',
   'DATA'),
  ('sre_parse.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\sre_parse.pyc',
   'DATA'),
  ('types.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\types.pyc',
   'DATA'),
  ('_bootlocale.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_bootlocale.pyc',
   'DATA'),
  ('keyword.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\keyword.pyc',
   'DATA'),
  ('enum.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\enum.pyc',
   'DATA'),
  ('weakref.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\weakref.pyc',
   'DATA'),
  ('sre_compile.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\sre_compile.pyc',
   'DATA'),
  ('functools.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\functools.pyc',
   'DATA'),
  ('re.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\re.pyc',
   'DATA'),
  ('locale.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\locale.pyc',
   'DATA'),
  ('operator.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\operator.pyc',
   'DATA'),
  ('ntpath.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ntpath.pyc',
   'DATA'),
  ('copyreg.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\copyreg.pyc',
   'DATA'),
  ('collections\\abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\collections\\abc.pyc',
   'DATA'),
  ('collections\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\collections\\__init__.pyc',
   'DATA'),
  ('encodings\\zlib_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\zlib_codec.pyc',
   'DATA'),
  ('encodings\\uu_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\uu_codec.pyc',
   'DATA'),
  ('encodings\\utf_8_sig.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_8_sig.pyc',
   'DATA'),
  ('encodings\\utf_8.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_8.pyc',
   'DATA'),
  ('encodings\\utf_7.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_7.pyc',
   'DATA'),
  ('encodings\\utf_32_le.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_32_le.pyc',
   'DATA'),
  ('encodings\\utf_32_be.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_32_be.pyc',
   'DATA'),
  ('encodings\\utf_32.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_32.pyc',
   'DATA'),
  ('encodings\\utf_16_le.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_16_le.pyc',
   'DATA'),
  ('encodings\\utf_16_be.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_16_be.pyc',
   'DATA'),
  ('encodings\\utf_16.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\utf_16.pyc',
   'DATA'),
  ('encodings\\unicode_escape.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\unicode_escape.pyc',
   'DATA'),
  ('encodings\\undefined.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\undefined.pyc',
   'DATA'),
  ('encodings\\tis_620.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\tis_620.pyc',
   'DATA'),
  ('encodings\\shift_jisx0213.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\shift_jisx0213.pyc',
   'DATA'),
  ('encodings\\shift_jis_2004.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\shift_jis_2004.pyc',
   'DATA'),
  ('encodings\\shift_jis.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\shift_jis.pyc',
   'DATA'),
  ('encodings\\rot_13.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\rot_13.pyc',
   'DATA'),
  ('encodings\\raw_unicode_escape.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\raw_unicode_escape.pyc',
   'DATA'),
  ('encodings\\quopri_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\quopri_codec.pyc',
   'DATA'),
  ('encodings\\punycode.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\punycode.pyc',
   'DATA'),
  ('encodings\\ptcp154.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\ptcp154.pyc',
   'DATA'),
  ('encodings\\palmos.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\palmos.pyc',
   'DATA'),
  ('encodings\\oem.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\oem.pyc',
   'DATA'),
  ('encodings\\mbcs.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mbcs.pyc',
   'DATA'),
  ('encodings\\mac_turkish.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_turkish.pyc',
   'DATA'),
  ('encodings\\mac_romanian.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_romanian.pyc',
   'DATA'),
  ('encodings\\mac_roman.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_roman.pyc',
   'DATA'),
  ('encodings\\mac_latin2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_latin2.pyc',
   'DATA'),
  ('encodings\\mac_iceland.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_iceland.pyc',
   'DATA'),
  ('encodings\\mac_greek.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_greek.pyc',
   'DATA'),
  ('encodings\\mac_farsi.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_farsi.pyc',
   'DATA'),
  ('encodings\\mac_cyrillic.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_cyrillic.pyc',
   'DATA'),
  ('encodings\\mac_croatian.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_croatian.pyc',
   'DATA'),
  ('encodings\\mac_arabic.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\mac_arabic.pyc',
   'DATA'),
  ('encodings\\latin_1.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\latin_1.pyc',
   'DATA'),
  ('encodings\\kz1048.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\kz1048.pyc',
   'DATA'),
  ('encodings\\koi8_u.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\koi8_u.pyc',
   'DATA'),
  ('encodings\\koi8_t.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\koi8_t.pyc',
   'DATA'),
  ('encodings\\koi8_r.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\koi8_r.pyc',
   'DATA'),
  ('encodings\\johab.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\johab.pyc',
   'DATA'),
  ('encodings\\iso8859_9.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_9.pyc',
   'DATA'),
  ('encodings\\iso8859_8.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_8.pyc',
   'DATA'),
  ('encodings\\iso8859_7.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_7.pyc',
   'DATA'),
  ('encodings\\iso8859_6.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_6.pyc',
   'DATA'),
  ('encodings\\iso8859_5.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_5.pyc',
   'DATA'),
  ('encodings\\iso8859_4.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_4.pyc',
   'DATA'),
  ('encodings\\iso8859_3.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_3.pyc',
   'DATA'),
  ('encodings\\iso8859_2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_2.pyc',
   'DATA'),
  ('encodings\\iso8859_16.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_16.pyc',
   'DATA'),
  ('encodings\\iso8859_15.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_15.pyc',
   'DATA'),
  ('encodings\\iso8859_14.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_14.pyc',
   'DATA'),
  ('encodings\\iso8859_13.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_13.pyc',
   'DATA'),
  ('encodings\\iso8859_11.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_11.pyc',
   'DATA'),
  ('encodings\\iso8859_10.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_10.pyc',
   'DATA'),
  ('encodings\\iso8859_1.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso8859_1.pyc',
   'DATA'),
  ('encodings\\iso2022_kr.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_kr.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_ext.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_ext.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_3.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_3.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_2004.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_2004.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_2.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_2.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_1.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp_1.pyc',
   'DATA'),
  ('encodings\\iso2022_jp.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\iso2022_jp.pyc',
   'DATA'),
  ('encodings\\idna.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\idna.pyc',
   'DATA'),
  ('stringprep.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\stringprep.pyc',
   'DATA'),
  ('encodings\\hz.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\hz.pyc',
   'DATA'),
  ('encodings\\hp_roman8.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\hp_roman8.pyc',
   'DATA'),
  ('encodings\\hex_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\hex_codec.pyc',
   'DATA'),
  ('encodings\\gbk.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\gbk.pyc',
   'DATA'),
  ('encodings\\gb2312.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\gb2312.pyc',
   'DATA'),
  ('encodings\\gb18030.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\gb18030.pyc',
   'DATA'),
  ('encodings\\euc_kr.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_kr.pyc',
   'DATA'),
  ('encodings\\euc_jp.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_jp.pyc',
   'DATA'),
  ('encodings\\euc_jisx0213.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_jisx0213.pyc',
   'DATA'),
  ('encodings\\euc_jis_2004.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\euc_jis_2004.pyc',
   'DATA'),
  ('encodings\\cp950.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp950.pyc',
   'DATA'),
  ('encodings\\cp949.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp949.pyc',
   'DATA'),
  ('encodings\\cp932.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp932.pyc',
   'DATA'),
  ('encodings\\cp875.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp875.pyc',
   'DATA'),
  ('encodings\\cp874.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp874.pyc',
   'DATA'),
  ('encodings\\cp869.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp869.pyc',
   'DATA'),
  ('encodings\\cp866.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp866.pyc',
   'DATA'),
  ('encodings\\cp865.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp865.pyc',
   'DATA'),
  ('encodings\\cp864.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp864.pyc',
   'DATA'),
  ('encodings\\cp863.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp863.pyc',
   'DATA'),
  ('encodings\\cp862.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp862.pyc',
   'DATA'),
  ('encodings\\cp861.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp861.pyc',
   'DATA'),
  ('encodings\\cp860.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp860.pyc',
   'DATA'),
  ('encodings\\cp858.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp858.pyc',
   'DATA'),
  ('encodings\\cp857.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp857.pyc',
   'DATA'),
  ('encodings\\cp856.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp856.pyc',
   'DATA'),
  ('encodings\\cp855.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp855.pyc',
   'DATA'),
  ('encodings\\cp852.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp852.pyc',
   'DATA'),
  ('encodings\\cp850.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp850.pyc',
   'DATA'),
  ('encodings\\cp775.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp775.pyc',
   'DATA'),
  ('encodings\\cp737.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp737.pyc',
   'DATA'),
  ('encodings\\cp720.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp720.pyc',
   'DATA'),
  ('encodings\\cp500.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp500.pyc',
   'DATA'),
  ('encodings\\cp437.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp437.pyc',
   'DATA'),
  ('encodings\\cp424.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp424.pyc',
   'DATA'),
  ('encodings\\cp273.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp273.pyc',
   'DATA'),
  ('encodings\\cp1258.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1258.pyc',
   'DATA'),
  ('encodings\\cp1257.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1257.pyc',
   'DATA'),
  ('encodings\\cp1256.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1256.pyc',
   'DATA'),
  ('encodings\\cp1255.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1255.pyc',
   'DATA'),
  ('encodings\\cp1254.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1254.pyc',
   'DATA'),
  ('encodings\\cp1253.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1253.pyc',
   'DATA'),
  ('encodings\\cp1252.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1252.pyc',
   'DATA'),
  ('encodings\\cp1251.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1251.pyc',
   'DATA'),
  ('encodings\\cp1250.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1250.pyc',
   'DATA'),
  ('encodings\\cp1140.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1140.pyc',
   'DATA'),
  ('encodings\\cp1125.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1125.pyc',
   'DATA'),
  ('encodings\\cp1026.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1026.pyc',
   'DATA'),
  ('encodings\\cp1006.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp1006.pyc',
   'DATA'),
  ('encodings\\cp037.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\cp037.pyc',
   'DATA'),
  ('encodings\\charmap.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\charmap.pyc',
   'DATA'),
  ('encodings\\bz2_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\bz2_codec.pyc',
   'DATA'),
  ('encodings\\big5hkscs.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\big5hkscs.pyc',
   'DATA'),
  ('encodings\\big5.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\big5.pyc',
   'DATA'),
  ('encodings\\base64_codec.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\base64_codec.pyc',
   'DATA'),
  ('encodings\\ascii.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\ascii.pyc',
   'DATA'),
  ('encodings\\aliases.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\aliases.pyc',
   'DATA'),
  ('encodings\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\encodings\\__init__.pyc',
   'DATA'),
  ('_collections_abc.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\_collections_abc.pyc',
   'DATA'),
  ('io.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\io.pyc',
   'DATA'),
  ('pymodbus\\client\\sync.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\client\\sync.pyc',
   'DATA'),
  ('pymodbus\\client\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\client\\__init__.pyc',
   'DATA'),
  ('pymodbus\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\__init__.pyc',
   'DATA'),
  ('pymodbus\\version.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\version.pyc',
   'DATA'),
  ('pymodbus\\client\\common.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\client\\common.pyc',
   'DATA'),
  ('pymodbus\\other_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\other_message.pyc',
   'DATA'),
  ('pymodbus\\compat.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\compat.pyc',
   'DATA'),
  ('imp.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\imp.pyc',
   'DATA'),
  ('socketserver.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\socketserver.pyc',
   'DATA'),
  ('six.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\six.pyc',
   'DATA'),
  ('__future__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\__future__.pyc',
   'DATA'),
  ('pymodbus\\device.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\device.pyc',
   'DATA'),
  ('pymodbus\\interfaces.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\interfaces.pyc',
   'DATA'),
  ('pymodbus\\pdu.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\pdu.pyc',
   'DATA'),
  ('pymodbus\\file_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\file_message.pyc',
   'DATA'),
  ('pymodbus\\diag_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\diag_message.pyc',
   'DATA'),
  ('pymodbus\\register_write_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\register_write_message.pyc',
   'DATA'),
  ('pymodbus\\register_read_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\register_read_message.pyc',
   'DATA'),
  ('pymodbus\\bit_write_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\bit_write_message.pyc',
   'DATA'),
  ('pymodbus\\bit_read_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\bit_read_message.pyc',
   'DATA'),
  ('pymodbus\\transaction.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\transaction.pyc',
   'DATA'),
  ('pymodbus\\framer\\binary_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\binary_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\__init__.pyc',
   'DATA'),
  ('pymodbus\\framer\\tls_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\tls_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\socket_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\socket_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\rtu_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\rtu_framer.pyc',
   'DATA'),
  ('pymodbus\\framer\\ascii_framer.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\framer\\ascii_framer.pyc',
   'DATA'),
  ('pymodbus\\exceptions.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\exceptions.pyc',
   'DATA'),
  ('pymodbus\\factory.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\factory.pyc',
   'DATA'),
  ('pymodbus\\mei_message.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\mei_message.pyc',
   'DATA'),
  ('pymodbus\\utilities.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\utilities.pyc',
   'DATA'),
  ('pymodbus\\constants.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\pymodbus\\constants.pyc',
   'DATA'),
  ('ssl.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ssl.pyc',
   'DATA'),
  ('serial\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\__init__.pyc',
   'DATA'),
  ('serial\\serialjava.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialjava.pyc',
   'DATA'),
  ('serial\\serialposix.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialposix.pyc',
   'DATA'),
  ('serial\\serialwin32.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialwin32.pyc',
   'DATA'),
  ('ctypes\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ctypes\\__init__.pyc',
   'DATA'),
  ('ctypes\\_endian.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ctypes\\_endian.pyc',
   'DATA'),
  ('serial\\win32.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\win32.pyc',
   'DATA'),
  ('ctypes\\wintypes.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\ctypes\\wintypes.pyc',
   'DATA'),
  ('serial\\serialcli.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialcli.pyc',
   'DATA'),
  ('serial\\serialutil.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\serial\\serialutil.pyc',
   'DATA'),
  ('PyQt5\\__init__.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\PyQt5\\__init__.pyc',
   'DATA'),
  ('datetime.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\datetime.pyc',
   'DATA'),
  ('csv.pyc',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\jidianqikongzhi\\build\\tiaoshi9\\localpycs\\0\\csv.pyc',
   'DATA')],
 [('zipfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipfile.py', 'PYMODULE'),
  ('argparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gettext.py', 'PYMODULE'),
  ('py_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\typing.py', 'PYMODULE'),
  ('configparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ipaddress.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fnmatch.py', 'PYMODULE'),
  ('email',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\calendar.py', 'PYMODULE'),
  ('socket', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\selectors.py',
   'PYMODULE'),
  ('random', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\errors.py',
   'PYMODULE'),
  ('tokenize', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bz2.py', 'PYMODULE'),
  ('contextlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextlib.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_strptime.py',
   'PYMODULE'),
  ('threading',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gzip.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ast.py', 'PYMODULE'),
  ('pkgutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipimport.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\abc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_py_abc.py', 'PYMODULE'),
  ('reprlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\reprlib.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_constants.py',
   'PYMODULE'),
  ('stat', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stat.py', 'PYMODULE'),
  ('posixpath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\posixpath.py',
   'PYMODULE'),
  ('os', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\os.py', 'PYMODULE'),
  ('subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\signal.py', 'PYMODULE'),
  ('codecs', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\codecs.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('warnings', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\warnings.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('linecache',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\linecache.py',
   'PYMODULE'),
  ('genericpath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\genericpath.py',
   'PYMODULE'),
  ('heapq', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\heapq.py', 'PYMODULE'),
  ('traceback',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\traceback.py',
   'PYMODULE'),
  ('sre_parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_parse.py',
   'PYMODULE'),
  ('types', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\types.py', 'PYMODULE'),
  ('_bootlocale',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_bootlocale.py',
   'PYMODULE'),
  ('keyword', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\keyword.py', 'PYMODULE'),
  ('enum', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\enum.py', 'PYMODULE'),
  ('weakref', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\weakref.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_compile.py',
   'PYMODULE'),
  ('functools',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\functools.py',
   'PYMODULE'),
  ('re', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\re.py', 'PYMODULE'),
  ('locale', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\locale.py', 'PYMODULE'),
  ('operator', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\operator.py', 'PYMODULE'),
  ('ntpath', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ntpath.py', 'PYMODULE'),
  ('copyreg', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copyreg.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stringprep.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('io', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\io.py', 'PYMODULE'),
  ('pymodbus.client.sync',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\sync.py',
   'PYMODULE'),
  ('pymodbus.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\__init__.py',
   'PYMODULE'),
  ('pymodbus',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\__init__.py',
   'PYMODULE'),
  ('pymodbus.version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\version.py',
   'PYMODULE'),
  ('pymodbus.client.common',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\client\\common.py',
   'PYMODULE'),
  ('pymodbus.other_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\other_message.py',
   'PYMODULE'),
  ('pymodbus.compat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\compat.py',
   'PYMODULE'),
  ('imp', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\imp.py', 'PYMODULE'),
  ('socketserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socketserver.py',
   'PYMODULE'),
  ('six',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('__future__',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\__future__.py',
   'PYMODULE'),
  ('pymodbus.device',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\device.py',
   'PYMODULE'),
  ('pymodbus.interfaces',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\interfaces.py',
   'PYMODULE'),
  ('pymodbus.pdu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\pdu.py',
   'PYMODULE'),
  ('pymodbus.file_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\file_message.py',
   'PYMODULE'),
  ('pymodbus.diag_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\diag_message.py',
   'PYMODULE'),
  ('pymodbus.register_write_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\register_write_message.py',
   'PYMODULE'),
  ('pymodbus.register_read_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\register_read_message.py',
   'PYMODULE'),
  ('pymodbus.bit_write_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\bit_write_message.py',
   'PYMODULE'),
  ('pymodbus.bit_read_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\bit_read_message.py',
   'PYMODULE'),
  ('pymodbus.transaction',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\transaction.py',
   'PYMODULE'),
  ('pymodbus.framer.binary_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\binary_framer.py',
   'PYMODULE'),
  ('pymodbus.framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\__init__.py',
   'PYMODULE'),
  ('pymodbus.framer.tls_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\tls_framer.py',
   'PYMODULE'),
  ('pymodbus.framer.socket_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\socket_framer.py',
   'PYMODULE'),
  ('pymodbus.framer.rtu_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\rtu_framer.py',
   'PYMODULE'),
  ('pymodbus.framer.ascii_framer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\framer\\ascii_framer.py',
   'PYMODULE'),
  ('pymodbus.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\exceptions.py',
   'PYMODULE'),
  ('pymodbus.factory',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\factory.py',
   'PYMODULE'),
  ('pymodbus.mei_message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\mei_message.py',
   'PYMODULE'),
  ('pymodbus.utilities',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\utilities.py',
   'PYMODULE'),
  ('pymodbus.constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pymodbus\\constants.py',
   'PYMODULE'),
  ('ssl', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ssl.py', 'PYMODULE'),
  ('serial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('datetime', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\datetime.py', 'PYMODULE'),
  ('csv', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\csv.py', 'PYMODULE')])
