import sys
import serial
import struct
import time
import csv
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QVBoxLayout,
    QMessageBox, QHBoxLayout, QLineEdit, QFileDialog
)
from PyQt5.QtCore import QTimer

# CRC16 计算函数
def crc16(data: bytes):
    crc = 0xFFFF
    for pos in data:
        crc ^= pos
        for _ in range(8):
            if crc & 0x0001:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc >>= 1
    return crc.to_bytes(2, byteorder='little')

# 解析返回数据
def parse_temp_humi(response: bytes):
    if len(response) < 9:
        raise ValueError("响应数据不足9字节")

    if response[1] != 0x03:
        raise ValueError("功能码错误")

    humidity_raw = int.from_bytes(response[3:5], 'big')
    humidity = humidity_raw / 10.0

    temp_raw = int.from_bytes(response[5:7], 'big', signed=True)
    temperature = temp_raw / 10.0

    return temperature, humidity

class TempHumiReader(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("温度湿度读取保存程序")
        self.setGeometry(200, 200, 400, 250)

        # 标签
        self.temp_label = QLabel("温度: -- ℃")
        self.humi_label = QLabel("湿度: -- %RH")

        # 串口输入框
        self.port_input = QLineEdit("COM3")

        # 控制按钮
        self.start_button = QPushButton("开始读取")
        self.start_button.clicked.connect(self.toggle_reading)

        self.save_button = QPushButton("开始保存")
        self.save_button.clicked.connect(self.start_saving)
        self.save_button.setEnabled(False)

        self.stop_save_button = QPushButton("停止保存")
        self.stop_save_button.clicked.connect(self.stop_saving)
        self.stop_save_button.setEnabled(False)

        self.select_dir_button = QPushButton("选择保存目录")
        self.select_dir_button.clicked.connect(self.select_directory)

        # 布局
        layout = QVBoxLayout()
        layout.addWidget(QLabel("串口号 (如 COM3):"))
        layout.addWidget(self.port_input)
        layout.addWidget(self.temp_label)
        layout.addWidget(self.humi_label)
        layout.addWidget(self.start_button)
        layout.addWidget(self.select_dir_button)
        layout.addWidget(self.save_button)
        layout.addWidget(self.stop_save_button)
        self.setLayout(layout)

        # 定时器
        self.timer = QTimer()
        self.timer.setInterval(1000)  # 5秒
        self.timer.timeout.connect(self.read_sensor)

        # 串口和保存配置
        self.serial = None
        self.save_data = False
        self.save_path = None

    def toggle_reading(self):
        if self.timer.isActive():
            self.timer.stop()
            self.start_button.setText("开始读取")
            if self.serial:
                self.serial.close()
        else:
            try:
                port_name = self.port_input.text().strip()
                self.serial = serial.Serial(port=port_name, baudrate=9600, timeout=1)
                self.timer.start()
                self.start_button.setText("停止读取")
            except Exception as e:
                QMessageBox.critical(self, "串口错误", f"打开串口失败: {e}")

    def select_directory(self):
        folder = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if folder:
            self.save_path = folder
            self.save_button.setEnabled(True)

    def start_saving(self):
        if not self.save_path:
            QMessageBox.warning(self, "保存目录未选择", "请先选择保存目录！")
            return
        self.save_data = True
        self.stop_save_button.setEnabled(True)
        self.save_button.setEnabled(False)

    def stop_saving(self):
        self.save_data = False
        self.stop_save_button.setEnabled(False)
        self.save_button.setEnabled(True)

    def read_sensor(self):
        try:
            request = bytes([0x01, 0x03, 0x00, 0x00, 0x00, 0x02])
            request += crc16(request)

            self.serial.write(request)
            response = self.serial.read(9)

            temp, humi = parse_temp_humi(response)

            self.temp_label.setText(f"温度: {temp:.1f} ℃")
            self.humi_label.setText(f"湿度: {humi:.1f} %RH")

            if self.save_data and self.save_path:
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                row = [now, f"{temp:.1f}", f"{humi:.1f}"]
                filename = f"{self.save_path}/温湿度记录.csv"
                with open(filename, mode='a', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerow(row)

        except Exception as e:
            self.temp_label.setText("温度: 错误")
            self.humi_label.setText("湿度: 错误")
            print(f"[错误] {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TempHumiReader()
    window.show()
    sys.exit(app.exec_())