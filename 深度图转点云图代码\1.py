import numpy as np
import cv2 as cv
# delay A in ns
delay = 37
# gatewidth A in ns
gw = 40

upper_pct = 0.987
lower_pct = 0.000
depth = 8
c = 3e8
src1 = cv.imread('a.bmp', cv.IMREAD_GRAYSCALE)
src2 = cv.imread('b.bmp', cv.IMREAD_GRAYSCALE)

upper = round(upper_pct * (1 << depth))
lower = round(lower_pct * (1 << depth))

R = delay * 1e-9 * c / 2
_, src1 = cv.threshold(src1, lower, 0, cv.THRESH_TOZERO)
_, src1 = cv.threshold(src1, upper, 0, cv.THRESH_TOZERO_INV)
_, src2 = cv.threshold(src2, lower, 0, cv.THRESH_TOZERO)
_, src2 = cv.threshold(src2, upper, 0, cv.THRESH_TOZERO_INV)

mask = np.zeros(src1.shape, np.uint8)
mask[(src1 > 0) & (src2 > 0)] = 255

src1 = src1.astype(np.float64)
src2 = src2.astype(np.float64)
# dist = src1 / src2
dist = np.divide(src1, src2, out = np.zeros_like(src1), where = mask != 0)
dist = R + gw * 1e-9 * c / 2 / (dist + 1)
dist[mask == 0] = 0

dist_norm = cv.normalize(dist, dist, 0, 255, cv.NORM_MINMAX, 8, mask)
dist_norm[mask == 0] = 0
res = cv.applyColorMap(dist_norm, cv.COLORMAP_TURBO)
res[mask == 0] = [0, 0, 0]
res = cv.cvtColor(res, cv.COLOR_BGR2RGB)
cv.imwrite('dist_3d.bmp', res)