import os
import shutil

# 源文件的公共路径和起始编号
src_folder = r'C:/Users/<USER>/Desktop'
dst_folder = r'F:/daman/0709/scan_0709_102453/yangdai/fenzu'

# 创建目标文件夹（如果不存在）
os.makedirs(dst_folder, exist_ok=True)

# 处理 pano25.jpg 到 pano62.jpg
start_num = 25
end_num = 62

for i, pano_num in enumerate(range(start_num, end_num + 1), start=1):
    src_file = os.path.join(src_folder, f'pano{pano_num}.jpg')
    dst_file = os.path.join(dst_folder, f'group_{i}.jpg')
    
    if os.path.exists(src_file):
        shutil.copy2(src_file, dst_file)
        print(f'复制: {src_file} -> {dst_file}')
    else:
        print(f'未找到文件: {src_file}')

print("图片复制和重命名完成。")
