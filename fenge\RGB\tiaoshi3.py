#使用神经网络分割
import cv2
import torch
import numpy as np
import segmentation_models_pytorch as smp
from torchvision import transforms
from PIL import Image

# 读取图像
img_path = "D:/fenge/1.bmp"
img = Image.open(img_path).convert('RGB')

# 初始化模型（这里用预训练的DeepLabV3+，num_classes=1表示二分类）
model = smp.DeepLabV3Plus(encoder_name="resnet34", encoder_weights="imagenet", in_channels=3, classes=1)
model.eval()

# 预处理
preprocess = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485,0.456,0.406], std=[0.229,0.224,0.225])
])
input_tensor = preprocess(img).unsqueeze(0)

# 推理
with torch.no_grad():
    output = model(input_tensor)
    output = torch.sigmoid(output)
    mask = (output[0][0] > 0.5).cpu().numpy().astype(np.uint8) * 255

# 将mask恢复到原图大小
mask = cv2.resize(mask, img.size, interpolation=cv2.INTER_NEAREST)

# 保存结果
cv2.imwrite("D:/fenge/pretrained_mask.png", mask)

print("预训练模型分割完成，结果已保存。")
