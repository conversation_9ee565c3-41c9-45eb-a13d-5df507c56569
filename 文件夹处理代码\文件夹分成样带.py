import os
import shutil

# 设置原始路径和目标路径
src_dir = 'F:/daman/0708/scan_0708_213652'
dest_dir = 'F:/daman/0708/scan_0708_213652/yangdai'

# 每组的文件夹数量
folders_per_group = 8

# 获取文件夹列表并按数字顺序排序
folders = sorted([f for f in os.listdir(src_dir) if os.path.isdir(os.path.join(src_dir, f))], key=lambda x: int(x))

# 每组分配指定数量的文件夹
for i in range(0, len(folders), folders_per_group):
    group_number = i // folders_per_group + 1
    group_name = f'样带{group_number}'
    group_path = os.path.join(dest_dir, group_name)

    # 创建分组文件夹
    os.makedirs(group_path, exist_ok=True)

    # 打印分组信息
    print(f'\n{group_name} 包含以下文件夹:')

    # 将每组的文件夹移动到新的分组文件夹中
    for folder in folders[i:i + folders_per_group]:
        print(f'  - {folder} 移动到 {group_name}')
        shutil.move(os.path.join(src_dir, folder), os.path.join(group_path, folder))

print("\n文件夹按数字顺序分组完成！")
