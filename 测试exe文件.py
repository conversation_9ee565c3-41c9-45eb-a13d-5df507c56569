#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试生成的exe文件是否正常工作
"""

import os
import subprocess
import time
from pathlib import Path

TARGET_DIR = r"D:\kongzhi"
EXE_FILE = "自动继电器温湿度监控系统.exe"

def test_exe_exists():
    """测试exe文件是否存在"""
    exe_path = Path(TARGET_DIR) / EXE_FILE
    if exe_path.exists():
        print(f"✅ exe文件存在: {exe_path}")
        print(f"📏 文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
        return True
    else:
        print(f"❌ exe文件不存在: {exe_path}")
        return False

def test_exe_launch():
    """测试exe文件是否能正常启动"""
    exe_path = Path(TARGET_DIR) / EXE_FILE
    
    print(f"🚀 尝试启动exe文件: {exe_path}")
    
    try:
        # 启动exe文件（非阻塞）
        process = subprocess.Popen([str(exe_path)], cwd=TARGET_DIR)
        
        print("✅ exe文件启动成功！")
        print(f"📊 进程ID: {process.pid}")
        
        # 等待几秒钟让程序完全启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 程序正在正常运行")
            
            # 询问是否终止测试进程
            choice = input("程序已启动，是否终止测试进程？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                    print("✅ 测试进程已终止")
                except subprocess.TimeoutExpired:
                    process.kill()
                    print("⚠️ 强制终止测试进程")
            else:
                print("ℹ️ 程序继续运行，您可以手动关闭")
            
            return True
        else:
            print(f"❌ 程序启动后立即退出，退出码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 启动exe文件失败: {e}")
        return False

def check_output_files():
    """检查输出目录中的所有文件"""
    target_path = Path(TARGET_DIR)
    
    print(f"\n📁 检查输出目录: {TARGET_DIR}")
    print("=" * 50)
    
    if not target_path.exists():
        print("❌ 目标目录不存在")
        return
    
    files = list(target_path.iterdir())
    if not files:
        print("📂 目录为空")
        return
    
    for item in sorted(files):
        if item.is_file():
            size = item.stat().st_size
            if size > 1024*1024:
                size_str = f"{size/(1024*1024):.1f} MB"
            elif size > 1024:
                size_str = f"{size/1024:.1f} KB"
            else:
                size_str = f"{size} B"
            
            print(f"📄 {item.name} ({size_str})")
        else:
            print(f"📁 {item.name}/")

def main():
    """主函数"""
    print("=" * 60)
    print("    自动继电器温湿度监控系统 - exe测试工具")
    print("=" * 60)
    print()
    
    # 检查输出文件
    check_output_files()
    print()
    
    # 测试exe文件是否存在
    if not test_exe_exists():
        print("❌ 测试失败：exe文件不存在")
        input("按回车键退出...")
        return
    
    print()
    
    # 询问是否测试启动
    choice = input("是否测试启动exe文件？(y/n): ").lower().strip()
    if choice in ['y', 'yes', '是', '']:
        print()
        if test_exe_launch():
            print("\n🎉 exe文件测试通过！")
        else:
            print("\n❌ exe文件测试失败")
    else:
        print("ℹ️ 跳过启动测试")
    
    print("\n📋 测试总结:")
    print("=" * 30)
    print(f"📁 输出目录: {TARGET_DIR}")
    print(f"🚀 主程序: {EXE_FILE}")
    print("📖 使用说明: 使用说明.md")
    print("🔧 启动脚本: 启动程序.bat")
    print("📄 说明文件: README.txt")
    
    print("\n💡 使用建议:")
    print("1. 双击'启动程序.bat'启动程序")
    print("2. 或直接双击exe文件")
    print("3. 程序会自动连接串口并开始监控")
    print("4. 数据会自动保存到'数据记录'文件夹")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
