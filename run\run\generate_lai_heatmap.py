import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from glob import glob
import re

def parse_scandata(scandata_path):
    """
    Parses LAI.csv and pose.csv from a given scandata_X folder.
    """
    lai_file = os.path.join(scandata_path, "LAI.csv")
    pose_file = os.path.join(scandata_path, "pose.csv")

    if not os.path.exists(lai_file) or not os.path.exists(pose_file):
        print(f"Warning: LAI.csv or pose.csv not found in {scandata_path}")
        return pd.DataFrame()

    try:
        lai_df = pd.read_csv(lai_file)
        pose_df = pd.read_csv(pose_file)

        # Clean angle data (remove '°' and convert to numeric)
        if 'horizontal' in pose_df.columns:
            pose_df['horizontal'] = pose_df['horizontal'].astype(str).str.replace('°', '').astype(float)
        if 'vertical' in pose_df.columns:
            pose_df['vertical'] = pose_df['vertical'].astype(str).str.replace('°', '').astype(float)
        
        # Merge dataframes
        # Ensure 'sequence' column exists and has a consistent type for merging
        if 'sequence' not in lai_df.columns or 'sequence' not in pose_df.columns:
            print(f"Warning: 'sequence' column missing in LAI.csv or pose.csv in {scandata_path}")
            return pd.DataFrame()

        lai_df['sequence'] = lai_df['sequence'].astype(int)
        pose_df['sequence'] = pose_df['sequence'].astype(int)

        merged_df = pd.merge(lai_df, pose_df, on=['date', 'scan', 'sequence'], how='inner')
        return merged_df
    except Exception as e:
        print(f"Error parsing files in {scandata_path}: {e}")
        return pd.DataFrame()

def collect_all_data(base_data_path):
    """
    Collects and parses data from all scandata_X folders under the base_data_path.
    The base_data_path should point to the directory containing date folders (e.g., '20250309').
    Returns a tuple: (DataFrame with all data, identified source date string or None).
    """
    all_data = []
    identified_source_date = None
    abs_base_data_path = os.path.abspath(base_data_path)

    # The following globbing logic is preserved from the previous version 
    # to correctly identify all scandata_X folders to process.
    date_pattern = os.path.join(abs_base_data_path, "[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]") # YYYYMMDD pattern
    date_folders_found_by_pattern = glob(date_pattern)
    
    scandata_folders_to_process = []
    if not date_folders_found_by_pattern:
        print(f"Warning: No date folders matching YYYYMMDD pattern found directly in {abs_base_data_path}. Checking all subdirectories and base for scandata_*")
        potential_parent_folders = glob(os.path.join(abs_base_data_path, "*"))
        scandata_folders_accumulator = []
        for p_folder in potential_parent_folders:
            if os.path.isdir(p_folder):
                scandata_folders_accumulator.extend(glob(os.path.join(p_folder, "scandata_*")))
        # Check if scandata_* is directly under base_data_path as well
        scandata_folders_accumulator.extend(glob(os.path.join(abs_base_data_path, "scandata_*")))
        scandata_folders_to_process = list(set(scandata_folders_accumulator)) # Unique paths
    else:
        for date_folder_path in date_folders_found_by_pattern:
            if os.path.isdir(date_folder_path):
                scandata_folders_to_process.extend(glob(os.path.join(date_folder_path, "scandata_*")))
        # Also include scandata_* if directly under a base_data_path that might not be a date folder itself
        # but contains date folders (this case might be redundant if base_data_path is a generic root)
        # To be safe and avoid missing scandata_* directly under base_data_path if date_folders_found_by_pattern was also true:
        scandata_folders_to_process.extend(glob(os.path.join(abs_base_data_path, "scandata_*")))
        scandata_folders_to_process = list(set(scandata_folders_to_process))

    unique_scandata_paths = sorted(list(set(scandata_folders_to_process)))

    if not unique_scandata_paths:
        print(f"No scandata_X folders found under {abs_base_data_path} or its relevant subdirectories.")

    for scandata_folder_abs_path in unique_scandata_paths:
        if os.path.isdir(scandata_folder_abs_path):
            print(f"Processing folder: {scandata_folder_abs_path}")
            data = parse_scandata(scandata_folder_abs_path)
            if not data.empty:
                all_data.append(data)
                if identified_source_date is None: # Try to set the date based on this first successful scandata_folder
                    parent_of_scandata = os.path.dirname(scandata_folder_abs_path)
                    grandparent_of_scandata = os.path.dirname(parent_of_scandata)
                    
                    # Case 1: scandata_folder is child of YYYYMMDD, which is a child of base_data_path
                    # e.g. abs_base_data_path = /data, parent_of_scandata = /data/20230101
                    if grandparent_of_scandata == abs_base_data_path and re.fullmatch(r"\d{8}", os.path.basename(parent_of_scandata)):
                        identified_source_date = os.path.basename(parent_of_scandata)
                    # Case 2: abs_base_data_path itself is YYYYMMDD, and scandata_folder is its direct child
                    # e.g. abs_base_data_path = /data/20230101, parent_of_scandata = /data/20230101
                    elif parent_of_scandata == abs_base_data_path and re.fullmatch(r"\d{8}", os.path.basename(abs_base_data_path)):
                        identified_source_date = os.path.basename(abs_base_data_path)
    
    if not all_data:
        return pd.DataFrame(), None
        
    return pd.concat(all_data, ignore_index=True), identified_source_date

def create_lai_heatmap(data_df, radial_bin_method='unique_vertical', output_image_path=None, default_angular_step_deg=1.0, source_display_name=None):
    """
    Creates and displays a fan-shaped LAI heatmap.
    
    Args:
        data_df (pd.DataFrame): DataFrame containing merged LAI and pose data 
                                (must include 'LAI', 'horizontal', 'vertical').
        radial_bin_method (str): Method to define radial bins. 
                                 'unique_vertical' uses unique sorted vertical angles.
        output_image_path (str): Path to save the generated heatmap image.
        default_angular_step_deg (float): Default width for an angular bin if only one unique horizontal angle is found.
        source_display_name (str, optional): Name of the data source (e.g., date) to display in the title.
    """
    if data_df.empty or not all(col in data_df.columns for col in ['LAI', 'horizontal', 'vertical']):
        print("Error: Input data is empty or missing required columns (LAI, horizontal, vertical).")
        return

    print("\n--- Debug Info for create_lai_heatmap ---")
    print(f"Input data_df shape: {data_df.shape}")
    print(data_df[['horizontal', 'vertical', 'LAI']].describe())

    # Determine angular range and bins based on unique horizontal values
    horizontal_levels_unique = np.sort(data_df['horizontal'].unique())
    print(f"Unique horizontal angle values: {horizontal_levels_unique}")

    if len(horizontal_levels_unique) == 0:
        print("Error: No unique horizontal angles found in data.")
        return
    elif len(horizontal_levels_unique) == 1:
        # Single unique horizontal angle, create one bin around it
        h_val = horizontal_levels_unique[0]
        # Use a small default step/width for this single bin
        angular_bins = np.array([h_val - default_angular_step_deg / 2, h_val + default_angular_step_deg / 2])
        print(f"Only one unique horizontal angle ({h_val} deg). Creating a single angular bin with width {default_angular_step_deg} deg.")
    else:
        # Multiple unique horizontal angles, calculate bin edges as midpoints
        angular_bin_edges = np.zeros(len(horizontal_levels_unique) + 1)
        angular_bin_edges[1:-1] = (horizontal_levels_unique[:-1] + horizontal_levels_unique[1:]) / 2.0
        # For the first and last edge, extrapolate based on the first/last interval
        angular_bin_edges[0] = horizontal_levels_unique[0] - (horizontal_levels_unique[1] - horizontal_levels_unique[0]) / 2.0
        angular_bin_edges[-1] = horizontal_levels_unique[-1] + (horizontal_levels_unique[-1] - horizontal_levels_unique[-2]) / 2.0
        angular_bins = np.unique(angular_bin_edges) # Ensure sorted and unique edges
        # Safety check if somehow np.unique reduces bins too much (should not happen with >1 unique levels)
        if len(angular_bins) < 2:
             angular_bins = np.array([horizontal_levels_unique[0] - default_angular_step_deg/2, horizontal_levels_unique[-1] + default_angular_step_deg/2])

    print(f"Number of angular bins created: {len(angular_bins) - 1}")
    print(f"Angular bin edges: {angular_bins}")

    if radial_bin_method == 'unique_vertical':
        radial_levels_unique = np.sort(np.abs(data_df['vertical']).unique()) 
        print(f"Unique absolute vertical values (radial levels): {radial_levels_unique}")
        if len(radial_levels_unique) < 2:
            print("Warning: Not enough unique radial levels to create distinct bins. Using a single radial bin.")
            if len(radial_levels_unique) == 1:
                radial_bins = np.array([radial_levels_unique[0] - 0.5, radial_levels_unique[0] + 0.5])
            else: 
                radial_bins = np.array([0,1]) 
        else:
            radial_bin_edges = np.zeros(len(radial_levels_unique) + 1)
            radial_bin_edges[1:-1] = (radial_levels_unique[:-1] + radial_levels_unique[1:]) / 2.0
            radial_bin_edges[0] = radial_levels_unique[0] - (radial_levels_unique[1] - radial_levels_unique[0]) / 2.0 if len(radial_levels_unique) > 1 else radial_levels_unique[0] - 0.5
            radial_bin_edges[-1] = radial_levels_unique[-1] + (radial_levels_unique[-1] - radial_levels_unique[-2]) / 2.0 if len(radial_levels_unique) > 1 else radial_levels_unique[0] + 0.5
            radial_bin_edges[0] = max(0, radial_bin_edges[0]) 
            radial_bins = np.unique(radial_bin_edges) 
            if len(radial_bins) < 2: 
                radial_bins = np.array([min(radial_levels_unique)-0.5, max(radial_levels_unique)+0.5])
                if radial_bins[0] < 0 : radial_bins[0] = 0
    else: 
        min_radial = np.abs(data_df['vertical']).min()
        max_radial = np.abs(data_df['vertical']).max()
        num_radial_bins = 10 
        radial_bins = np.linspace(min_radial, max_radial, num_radial_bins + 1)
        print(f"Using fallback radial bins (linspace): {num_radial_bins} bins")

    print(f"Number of radial bins created: {len(radial_bins) - 1}")

    lai_sum_grid = np.zeros((len(radial_bins) - 1, len(angular_bins) - 1))
    lai_count_grid = np.zeros_like(lai_sum_grid)

    for _, row in data_df.iterrows():
        h_angle = row['horizontal']
        v_angle_abs = np.abs(row['vertical'])
        lai_value = row['LAI']

        angular_idx = np.digitize(h_angle, angular_bins) - 1
        angular_idx = np.clip(angular_idx, 0, len(angular_bins) - 2)

        radial_idx = np.digitize(v_angle_abs, radial_bins) - 1
        radial_idx = np.clip(radial_idx, 0, len(radial_bins) - 2)

        if 0 <= angular_idx < lai_sum_grid.shape[1] and 0 <= radial_idx < lai_sum_grid.shape[0]:
            if not np.isnan(lai_value):
                lai_sum_grid[radial_idx, angular_idx] += lai_value
                lai_count_grid[radial_idx, angular_idx] += 1

    average_lai_grid = np.full_like(lai_sum_grid, np.nan)
    non_zero_counts = lai_count_grid > 0
    average_lai_grid[non_zero_counts] = lai_sum_grid[non_zero_counts] / lai_count_grid[non_zero_counts]
    
    print(f"Shape of the final LAI grid (radial_bins, angular_bins): {average_lai_grid.shape}")
    num_cells_with_data = np.count_nonzero(~np.isnan(average_lai_grid))
    print(f"Number of grid cells with actual data (non-NaN): {num_cells_with_data}")
    print("--- End Debug Info ---\n")

    angular_bins_rad = np.deg2rad(angular_bins)

    fig, ax = plt.subplots(subplot_kw={'projection': 'polar'})

    pcm = ax.pcolormesh(angular_bins_rad, radial_bins, average_lai_grid, cmap='viridis', shading='auto', vmin=0) 

    ax.set_theta_zero_location("N")  
    ax.set_theta_direction(-1)  
    ax.set_rmax(radial_bins[-1])

    plt.colorbar(pcm, ax=ax, label="Average LAI")
    
    title = "LAI Heatmap"
    if source_display_name:
        title += f" - {source_display_name}"
    plt.title(title)
    
    if output_image_path:
        try:
            plt.savefig(output_image_path, bbox_inches='tight', dpi=300)
            print(f"Heatmap saved to {output_image_path}")
        except Exception as e:
            print(f"Error saving heatmap: {e}")

    plt.show()

def main():
    # --- Configuration ---
    base_data_path = "." 
    # angular_res = 10 # This is now determined by unique horizontal values
    # Default output file name if source date cannot be determined
    default_output_filename = "LAI_heatmap.png"
    # Default step for angular bins if only one unique horizontal angle is found
    default_h_step = 1.0 
    # --- End Configuration ---

    print(f"Starting LAI data collection from: {os.path.abspath(base_data_path)}")
    all_lai_data, identified_date_str = collect_all_data(base_data_path)

    if all_lai_data.empty:
        print("No data collected. Exiting.")
        return

    print(f"Collected {len(all_lai_data)} data points.")
    if identified_date_str:
        print(f"Identified source date: {identified_date_str}")
        output_file = f"LAI_{identified_date_str}.png"
        plot_title_suffix = identified_date_str
    else:
        print("Could not identify a specific source date from folder structure for filename/title.")
        # Fallback: try to use base_data_path's name if it looks like a date
        base_name = os.path.basename(os.path.abspath(base_data_path))
        if re.fullmatch(r"\d{8}", base_name):
            output_file = f"LAI_{base_name}.png"
            plot_title_suffix = base_name
            print(f"Using base name of input path for filename/title: {base_name}")
        else:
            output_file = default_output_filename
            plot_title_suffix = None

    print(f"Output image will be saved to: {os.path.abspath(output_file)}")
    print("Sample of collected data:")
    print(all_lai_data.head())

    print("Generating LAI heatmap...")
    create_lai_heatmap(all_lai_data, output_image_path=output_file, default_angular_step_deg=default_h_step, source_display_name=plot_title_suffix)
    print("Heatmap generation process finished.")

if __name__ == "__main__":
    main() 