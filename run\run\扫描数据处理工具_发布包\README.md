# 扫描数据处理工具

这是一个用于处理扫描数据的图形界面工具，可以将包含多个scan_*目录的数据处理成标准格式。

## 功能特点

- 🖥️ 友好的图形界面
- 📁 支持选择源文件夹和输出文件夹
- 🔄 自动处理多个scan_*目录
- 📊 生成pose.csv文件
- 🖼️ 智能图像分类和转换
- 📝 实时处理日志显示

## 图像处理规则

- **NIR + Depth模式**: 当ori_bmp和res_bmp都有文件时
  - ori_bmp → 2D-NIR
  - res_bmp → 3D-depth
  
- **RGB模式**: 当只有ori_bmp有文件时
  - ori_bmp → 2D-RGB

## 使用方法

### 方法一：直接运行exe文件
1. 下载并运行 `扫描数据处理工具.exe`
2. 点击"浏览"按钮选择包含scan_*文件夹的源目录
3. 点击"浏览"按钮选择输出目录
4. 点击"开始处理"按钮
5. 等待处理完成

### 方法二：从源码运行
```bash
# 安装依赖
pip install pillow

# 运行程序
python scan_processor_gui.py
```

## 打包成exe文件

### 自动打包（推荐）
双击运行 `build.bat` 文件，脚本会自动：
1. 检查Python环境
2. 安装必要依赖
3. 打包成exe文件

### 手动打包
```bash
# 安装PyInstaller
pip install pyinstaller pillow

# 打包命令
pyinstaller --onefile --windowed --name="扫描数据处理工具" scan_processor_gui.py
```

打包完成后，exe文件位于 `dist` 文件夹中。

## 输出结构

处理完成后会生成如下目录结构：
```
输出目录/
└── 20250708/          # 日期文件夹
    ├── scandata_1/     # 第一个scan目录的数据
    │   ├── 1/
    │   │   ├── 2D-NIR/
    │   │   ├── 2D-RGB/
    │   │   └── 3D-depth/
    │   ├── 2/
    │   ├── ...
    │   └── pose.csv
    ├── scandata_2/     # 第二个scan目录的数据
    ├── scandata_3/
    └── scandata_4/
```

## 系统要求

- Windows 7/8/10/11
- Python 3.6+ (如果从源码运行)
- 足够的磁盘空间用于处理图像文件

## 注意事项

1. 确保源目录包含正确格式的scan_*文件夹
2. 处理大量数据时请确保有足够的磁盘空间
3. 处理过程中请不要关闭程序
4. 建议在处理前备份重要数据

## 故障排除

**问题**: 程序无法启动
- 解决: 确保已安装Python和必要依赖

**问题**: 找不到scan_*目录
- 解决: 检查源目录是否包含以"scan_"开头的文件夹

**问题**: 图像转换失败
- 解决: 检查图像文件是否损坏，确保有足够磁盘空间

**问题**: 处理速度慢
- 解决: 这是正常现象，图像处理需要时间，请耐心等待

## 技术支持

如有问题，请检查处理日志中的错误信息，或联系技术支持。
