===============================================
           扫描数据处理工具 - 版本信息
===============================================

📦 软件信息
-----------
软件名称：扫描数据处理工具
版本号：v1.0
发布日期：2025年7月11日
开发语言：Python 3.9
界面框架：Tkinter
图像处理：PIL (Pillow)
打包工具：PyInstaller

🎯 主要功能
-----------
✓ 图形界面操作，简单易用
✓ 批量处理scan_*目录
✓ 智能图像分类和转换
✓ 自动生成pose.csv文件
✓ 支持NIR+Depth和RGB两种模式
✓ 实时处理日志显示
✓ 错误处理和异常恢复

📊 处理能力
-----------
- 支持BMP到JPG格式转换
- 自动图像尺寸调整
- 批量目录处理
- 角度数据提取
- CSV文件生成

🔧 技术特性
-----------
- 多线程处理，界面不卡顿
- 内存优化，支持大文件处理
- 异常处理，提高稳定性
- 中文界面，操作友好
- 单文件exe，无需安装

📋 更新日志
-----------
v1.0 (2025-07-11)
- 初始版本发布
- 实现基本的扫描数据处理功能
- 支持图形界面操作
- 添加详细的使用说明

🛠️ 系统要求
-----------
- 操作系统：Windows 7/8/10/11 (64位)
- 内存：建议4GB以上
- 磁盘空间：处理数据的2-3倍空间
- 显示器：1024x768以上分辨率

📞 联系方式
-----------
如有问题或建议，请联系技术支持。

===============================================
© 2025 扫描数据处理工具 保留所有权利
===============================================
