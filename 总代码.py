import os
import shutil
from PIL import Image

# ------------------路径和参数设置------------------

# 根目录路径
source_root_dir = r'H:/燕山站1028-张钰润/scan_1028_174738'  # 样本根目录

# 基于根目录自动生成其他路径
grouped_dir = os.path.join(source_root_dir, '分组样带')  # 分组后的目标目录
destination_dir = os.path.join(source_root_dir, '提取灰度图片结果60nsA')  # 提取图片保存的目标目录
cropped_output_dir = os.path.join(source_root_dir, '灰度图片裁剪结果60nsA')  # 裁剪结果保存目录
output_directory = os.path.join(source_root_dir, '灰度图片拼接结果60nsA')  # 拼接结果保存目录

# 分组参数
folders_per_group = 16  # 每组的文件夹数量
group_size = 16  # 每组的图片数量

# 裁剪参数
width_crop = 1608  # 水平方向保留的像素宽度
x_crop_values = [325, 321, 310, 322, 312, 314, 317, 316, 310, 325, 321, 310, 322, 312, 314, 317, 316, 310]  # 每个图像对应的裁剪值
y_crop = 0  # 下边去掉的像素数

# # ------------------步骤 1：将文件夹分成样带并打印分组细节------------------

# 创建分组目录
os.makedirs(grouped_dir, exist_ok=True)

# 获取文件夹列表并按数字顺序排序，只包含能够转换为数字的文件夹
folders = sorted([f for f in os.listdir(source_root_dir)
                  if os.path.isdir(os.path.join(source_root_dir, f)) and f.isdigit()],
                 key=lambda x: int(x))

# 将文件夹分成指定数量的样带，并打印分组信息
for i in range(0, len(folders), folders_per_group):
    group_number = i // folders_per_group + 1
    group_name = f'样带{group_number}'
    group_path = os.path.join(grouped_dir, group_name)

    # 创建样带文件夹
    os.makedirs(group_path, exist_ok=True)
    print(f'\n创建 {group_name} 包含以下文件夹:')

    # 将文件夹复制到分组文件夹中并打印包含的文件夹
    for folder in folders[i:i + folders_per_group]:
        print(f'  - {folder} 已复制到 {group_name}')
        shutil.move(os.path.join(source_root_dir, folder), os.path.join(group_path, folder))

print("文件夹分组完成\n")

# ------------------步骤 2：提取图片并重命名保存------------------

# 创建提取图片保存目录
os.makedirs(destination_dir, exist_ok=True)
image_counter = 1  # 初始化图片计数器

# 从每个分组中提取图片
group_folders = sorted(os.listdir(grouped_dir), key=lambda x: int(x[2:]))  # 按样带顺序排序

for group_folder in group_folders:
    group_path = os.path.join(grouped_dir, group_folder)

    if os.path.isdir(group_path):
        sub_folders = sorted(os.listdir(group_path), key=lambda x: int(x))  # 按文件夹顺序排序
        for sub_folder in sub_folders:
            sub_folder_path = os.path.join(group_path, sub_folder)

            if os.path.isdir(sub_folder_path):
                # 构建 "37.00 ns\res_bmp" 路径
                res_bmp_path = os.path.join(sub_folder_path, '60.00 ns', 'ori_bmp')

                # 检查 res_bmp 文件夹是否存在
                if os.path.exists(res_bmp_path):
                    images = sorted([f for f in os.listdir(res_bmp_path) if f.endswith(('.bmp', '.png', '.jpg'))])
                    if images:
                        first_image = images[0]
                        source_image_path = os.path.join(res_bmp_path, first_image)

                        # 命名为 "1.bmp", "2.bmp", 依次类推
                        destination_image_path = os.path.join(destination_dir, f'{image_counter}.bmp')

                        # 复制并重命名图片
                        shutil.copyfile(source_image_path, destination_image_path)
                        print(f"已提取: {source_image_path} -> 保存为: {destination_image_path}")

                        # 增加图片计数器
                        image_counter += 1

print("\n图片提取并保存完成\n")

# ------------------步骤 3：裁剪提取的图片------------------

# 创建裁剪结果保存目录
os.makedirs(cropped_output_dir, exist_ok=True)

# 获取目标目录中所有 .bmp 文件，并按文件名从小到大排序
bmp_files = sorted([f for f in os.listdir(destination_dir) if f.endswith('.bmp')], key=lambda x: int(x.split('.')[0]))

# 循环处理每个 .bmp 文件
for idx, img_file in enumerate(bmp_files):
    img_path = os.path.join(destination_dir, img_file)
    img = Image.open(img_path)
    x_crop = x_crop_values[idx % len(x_crop_values)]  # 动态获取裁剪值

    # 裁剪图片右边，保留 width_crop 个像素宽度
    cropped_img = img.crop((0, x_crop, width_crop, img.height - y_crop))

    # 生成裁剪后图片的输出路径
    output_image_path = os.path.join(cropped_output_dir, img_file)

    # 保存裁剪后的图像
    cropped_img.save(output_image_path)

    print(f"{img_file} 已裁剪并保存至 {output_image_path}")

print("\n所有图片裁剪并保存完成")

# ------------------拼接步骤 4：拼接裁剪后的图片------------------

# 确保输出拼接目录存在
os.makedirs(output_directory, exist_ok=True)

# 获取目标目录中所有 .bmp 文件，并按文件名从小到大排序
bmp_files = sorted([f for f in os.listdir(cropped_output_dir) if f.endswith('.bmp')], key=lambda x: int(x.split('.')[0]))

# 计算组的数量
total_images = len(bmp_files)
total_groups = (total_images + group_size - 1) // group_size  # 向上取整

# 循环每组处理图片
for group_number in range(total_groups):
    # 确定该组的图片范围
    start_idx = group_number * group_size
    end_idx = min(start_idx + group_size, total_images)
    group_files = bmp_files[start_idx:end_idx]

    cropped_images = []

    # 打开并裁剪该组的图片
    for img_file in group_files:
        img_path = os.path.join(cropped_output_dir, img_file)
        img = Image.open(img_path)
        cropped_images.append(img)

    # 获取总的拼接图像高度
    total_height = sum(img.height for img in cropped_images)
    final_width = cropped_images[0].width  # 所有图像的宽度一致

    # 创建一个新图像用于拼接
    final_image = Image.new('RGB', (final_width, total_height))

    # 打印拼接顺序信息
    if (group_number + 1) % 2 == 1:
        print(f"\n样带 {group_number + 1} 采取倒序拼接，从上到下的图片顺序为：")
        for i, img in enumerate(reversed(cropped_images), start=1):
            print(f"  第 {i} 张图片：{group_files[-i]}")
    else:
        print(f"\n样带 {group_number + 1} 采取正序拼接，从上到下的图片顺序为：")
        for i, img_file in enumerate(group_files, start=1):
            print(f"  第 {i} 张图片：{img_file}")

    # 按奇偶性决定拼接顺序
    current_y = 0
    if (group_number + 1) % 2 == 1:  # 奇数样带倒序拼接
        for img in reversed(cropped_images):
            final_image.paste(img, (0, current_y))
            current_y += img.height
    else:  # 偶数样带正序拼接
        for img in cropped_images:
            final_image.paste(img, (0, current_y))
            current_y += img.height

    # 生成输出文件名
    output_image = os.path.join(output_directory, f'样带{group_number + 1}.bmp')

    # 保存最终拼接的图像
    final_image.save(output_image)

    print(f"样带 {group_number + 1} 的拼接图像已保存至 {output_image}")
