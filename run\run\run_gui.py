#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI应用启动器
简化启动过程并提供错误处理
"""

import sys
import os

def is_packaged_executable():
    """检查是否运行在PyInstaller打包的可执行文件中"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

def safe_input(prompt="按回车键退出..."):
    """安全的输入函数，在打包环境中不会崩溃"""
    if is_packaged_executable():
        # 在打包的可执行文件中，不需要等待用户输入
        return
    else:
        # 在开发环境中，正常等待用户输入
        try:
            return input(prompt)
        except (EOFError, KeyboardInterrupt):
            return

def check_dependencies():
    """检查依赖项"""
    # 在打包的可执行文件中，依赖项已经包含，跳过检查
    if is_packaged_executable():
        return True
        
    missing_deps = []
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import PyQt5
    except ImportError:
        missing_deps.append("PyQt5")
    
    if missing_deps:
        print("❌ 缺少以下依赖项:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装依赖项:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def show_error_dialog(title, message):
    """显示错误对话框（仅在打包环境中使用）"""
    if not is_packaged_executable():
        return
        
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        import sys
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 显示错误消息框
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
        
    except Exception:
        # 如果无法显示对话框，静默失败
        pass

def main():
    """主函数"""
    # 在打包环境中不显示控制台输出
    if not is_packaged_executable():
        print("🚀 智能网关数据传输工具")
        print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        if is_packaged_executable():
            show_error_dialog("依赖错误", "缺少必要的依赖项，请重新安装程序。")
        else:
            safe_input("\n按回车键退出...")
        return
    
    # 在打包环境中，文件已经内嵌，跳过文件检查
    if not is_packaged_executable():
        # 检查核心文件
        if not os.path.exists("data_transmission.py"):
            print("❌ 找不到 data_transmission.py 文件")
            safe_input("按回车键退出...")
            return
        
        if not os.path.exists("gui_app.py"):
            print("❌ 找不到 gui_app.py 文件")
            safe_input("按回车键退出...")
            return
        
        print("✅ 依赖检查通过")
        print("🎯 启动GUI应用...")
    
    try:
        # 导入并运行GUI应用
        from gui_app import main as gui_main
        gui_main()
    except Exception as e:
        error_msg = f"启动失败: {e}"
        
        if is_packaged_executable():
            # 在打包环境中显示错误对话框
            import traceback
            detailed_error = f"启动失败: {e}\n\n错误详情:\n{traceback.format_exc()}"
            show_error_dialog("应用程序启动失败", detailed_error)
        else:
            # 在开发环境中显示控制台错误
            print(f"❌ {error_msg}")
            print("\n错误详情:")
            import traceback
            traceback.print_exc()
            safe_input("按回车键退出...")

if __name__ == "__main__":
    main() 