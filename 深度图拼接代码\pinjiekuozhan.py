from PIL import Image
import os
import cv2
import numpy as np

# ---------- 支持中文路径读取的函数 ----------
def read_image_unicode(path):
    with Image.open(path) as img:
        img = img.convert('L')  # 灰度图
        return np.array(img)

# ---------- 自动计算组内上下裁剪量 ----------
def compute_group_crop_offsets(image_paths):
    sift = cv2.SIFT_create()
    dy_list = []

    for i in range(1, len(image_paths)):
        img1 = read_image_unicode(image_paths[i - 1])
        img2 = read_image_unicode(image_paths[i])
        
        kp1, des1 = sift.detectAndCompute(img1, None)
        kp2, des2 = sift.detectAndCompute(img2, None)
        
        if des1 is None or des2 is None:
            continue
        
        bf = cv2.BFMatcher()
        matches = bf.knnMatch(des1, des2, k=2)

        good_matches = [m for m, n in matches if m.distance < 0.75 * n.distance]
        if len(good_matches) < 5:
            continue

        for m in good_matches:
            dy = kp1[m.queryIdx].pt[1] - kp2[m.trainIdx].pt[1]
            dy_list.append(dy)

    if not dy_list:
        print("警告：组内特征点匹配不足，使用默认裁剪参数")
        return 210, 100  # 默认裁剪参数

    dy_median = np.median(dy_list)
    x_crop = int(max(0, dy_median))
    y_crop = int(max(0, -dy_median))
    return x_crop, y_crop


# ---------- 拼接主逻辑 ----------
width_crop = 1608  # 保留宽度
group_size = 7     # 每组图片数量

input_directory = r'G:/大满站数据/0704/scan_0704_221357/yangdai/photo_3D_80ns/photo_3D_80ns_crop'
output_directory = os.path.join(input_directory, 'output')
os.makedirs(output_directory, exist_ok=True)

bmp_files = sorted([f for f in os.listdir(input_directory) if f.endswith('.bmp')],
                   key=lambda x: int(os.path.splitext(x)[0]))

total_images = len(bmp_files)
total_groups = (total_images + group_size - 1) // group_size

for group_number in range(total_groups):
    start_idx = group_number * group_size
    end_idx = min(start_idx + group_size, total_images)
    group_files = bmp_files[start_idx:end_idx]
    group_paths = [os.path.join(input_directory, f) for f in group_files]

    # 计算该组自动裁剪参数
    x_crop, y_crop = compute_group_crop_offsets(group_paths)
    print(f"样带 {group_number + 1} 的裁剪参数：x_crop = {x_crop}, y_crop = {y_crop}")

    cropped_images = []
    for img_path in group_paths:
        img = Image.open(img_path)
        cropped_img = img.crop((0, x_crop, width_crop, img.height - y_crop))
        cropped_images.append(cropped_img)

    total_height = sum(img.height for img in cropped_images)
    final_width = cropped_images[0].width
    final_image = Image.new('RGB', (final_width, total_height))

    current_y = 0
    if (group_number + 1) % 2 == 0:
        for img in reversed(cropped_images):
            final_image.paste(img, (0, current_y))
            current_y += img.height
    else:
        for img in cropped_images:
            final_image.paste(img, (0, current_y))
            current_y += img.height

    output_image_path = os.path.join(output_directory, f'样带{group_number + 1}.bmp')
    final_image.save(output_image_path)
    print(f"样带 {group_number + 1} 已保存：{output_image_path}")
