import sys
import csv
import os
from datetime import datetime, time as dtime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, QLineEdit,
    QMessageBox, QGroupBox, QTextEdit, QTimeEdit, QGridLayout, QCheckBox, QFileDialog
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient as ModbusClient
import serial.tools.list_ports


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("继电器和温湿度传感器综合控制器")
        self.resize(700, 650)

        self.relay_slave_id = 0x11
        self.sensor_slave_id = 0x01

        self.modbus_client = None
        self.save_data = True  # 默认开启保存
        self.save_path = None
        self.poll_flag = True

        self.relay_action_log = []
        self.run_status_log = []
        self.temp_humi_data = []  # 存储温湿度数据

        # 自动创建保存目录
        self.create_daily_folder()

        self.init_ui()

        # 轮询设备定时器（每秒）
        self.poll_timer = QTimer()
        self.poll_timer.timeout.connect(self.poll_devices)

        # 运行状态记录定时器（每10秒）
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.record_run_status)
        self.status_timer_interval_ms = 10 * 1000

        # 数据保存定时器（每10分钟）
        self.save_timer = QTimer()
        self.save_timer.timeout.connect(self.save_data_to_file)
        self.save_timer_interval_ms = 10 * 60 * 1000  # 10分钟

        # 程序启动后自动尝试连接串口
        QTimer.singleShot(1000, self.auto_connect_serial)

    def create_daily_folder(self):
        """创建以当前日期命名的文件夹"""
        today = datetime.now().strftime("%Y-%m-%d")
        self.save_path = os.path.join(os.getcwd(), "数据记录", today)

        try:
            os.makedirs(self.save_path, exist_ok=True)
            print(f"创建/使用数据保存目录: {self.save_path}")
        except Exception as e:
            print(f"创建目录失败: {e}")
            self.save_path = os.getcwd()

    def auto_connect_serial(self):
        """自动检测并连接串口"""
        ports = list(serial.tools.list_ports.comports())
        if not ports:
            self.log("❌ 未检测到可用串口")
            return

        # 尝试连接第一个可用串口
        for port in ports:
            try:
                self.port_input.setText(port.device)
                self.log(f"🔍 尝试连接串口: {port.device}")
                self.connect_serial()
                break
            except Exception as e:
                self.log(f"❌ 连接 {port.device} 失败: {e}")
                continue

    def init_ui(self):
        layout = QVBoxLayout()

        # 连接状态显示区域
        status_group = QGroupBox("连接状态")
        status_layout = QVBoxLayout()

        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("串口号:"))
        self.port_input = QLineEdit("COM7")
        port_layout.addWidget(self.port_input)

        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        port_layout.addWidget(self.connect_btn)

        self.status_label = QLabel("状态：正在自动连接...")
        port_layout.addWidget(self.status_label)
        status_layout.addLayout(port_layout)

        # 实时信息显示
        info_layout = QHBoxLayout()
        self.connection_status = QLabel("🔴 未连接")
        self.data_save_status = QLabel("💾 数据保存: 开启")
        info_layout.addWidget(self.connection_status)
        info_layout.addWidget(self.data_save_status)
        status_layout.addLayout(info_layout)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        relay_group = QGroupBox("DO继电器控制")
        relay_layout = QGridLayout()

        self.do_buttons = []
        self.auto_checkboxes = []
        self.start_time_edits = []
        self.end_time_edits = []

        for i in range(4):
            relay_layout.addWidget(QLabel(f"DO{i + 1}"), i, 0)
            btn = QPushButton("断开")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, idx=i: self.manual_toggle_do(idx, checked))
            relay_layout.addWidget(btn, i, 1)
            self.do_buttons.append(btn)

            auto_chk = QCheckBox("定时")
            relay_layout.addWidget(auto_chk, i, 2)
            self.auto_checkboxes.append(auto_chk)

            relay_layout.addWidget(QLabel("开始时间"), i, 3)
            start_time = QTimeEdit()
            start_time.setDisplayFormat("HH:mm")
            start_time.setTime(dtime(8, 0))
            relay_layout.addWidget(start_time, i, 4)
            self.start_time_edits.append(start_time)

            relay_layout.addWidget(QLabel("结束时间"), i, 5)
            end_time = QTimeEdit()
            end_time.setDisplayFormat("HH:mm")
            end_time.setTime(dtime(18, 0))
            relay_layout.addWidget(end_time, i, 6)
            self.end_time_edits.append(end_time)

        relay_group.setLayout(relay_layout)
        layout.addWidget(relay_group)

        # 温湿度传感器显示区域
        sensor_group = QGroupBox("温湿度传感器 - 实时监控")
        sensor_layout = QVBoxLayout()

        temp_humi_layout = QHBoxLayout()
        self.temp_label = QLabel("🌡️ 温度: -- ℃")
        self.temp_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #FF6B35;")
        self.humi_label = QLabel("💧 湿度: -- %RH")
        self.humi_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #4A90E2;")
        temp_humi_layout.addWidget(self.temp_label)
        temp_humi_layout.addWidget(self.humi_label)
        sensor_layout.addLayout(temp_humi_layout)

        # 数据保存信息
        save_info_layout = QHBoxLayout()
        self.last_save_label = QLabel("📁 保存目录: " + self.save_path)
        self.next_save_label = QLabel("⏰ 下次保存: 10分钟后")
        save_info_layout.addWidget(self.last_save_label)
        save_info_layout.addWidget(self.next_save_label)
        sensor_layout.addLayout(save_info_layout)

        sensor_group.setLayout(sensor_layout)
        layout.addWidget(sensor_group)

        # 控制按钮区域
        control_group = QGroupBox("数据管理")
        control_layout = QHBoxLayout()

        self.export_relay_log_btn = QPushButton("导出继电器动作日志")
        self.export_relay_log_btn.clicked.connect(self.export_relay_action_log)
        self.export_relay_log_btn.setEnabled(False)
        control_layout.addWidget(self.export_relay_log_btn)

        self.export_status_log_btn = QPushButton("导出运行状态日志")
        self.export_status_log_btn.clicked.connect(self.export_run_status_log)
        self.export_status_log_btn.setEnabled(False)
        control_layout.addWidget(self.export_status_log_btn)

        self.export_temp_humi_btn = QPushButton("导出温湿度数据")
        self.export_temp_humi_btn.clicked.connect(self.export_temp_humi_data)
        self.export_temp_humi_btn.setEnabled(False)
        control_layout.addWidget(self.export_temp_humi_btn)

        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        layout.addWidget(QLabel("日志："))
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def toggle_connection(self):
        if self.modbus_client:
            self.disconnect_all()
            return
        self.connect_serial()

    def connect_serial(self):
        """连接串口"""
        port_name = self.port_input.text().strip()
        try:
            self.modbus_client = ModbusClient(method='rtu', port=port_name, baudrate=9600, timeout=1)
            if not self.modbus_client.connect():
                raise Exception("Modbus 设备连接失败")

            self.status_label.setText(f"状态：已连接 {port_name}")
            self.connection_status.setText("🟢 已连接")
            self.connection_status.setStyleSheet("color: green; font-weight: bold;")
            self.connect_btn.setText("断开串口")
            self.set_controls_enabled(True)
            self.log(f"✅ 串口 {port_name} 连接成功")

            # 启动所有定时器
            self.poll_timer.start(1000)  # 每秒轮询
            self.status_timer.start(self.status_timer_interval_ms)  # 每10秒记录状态
            self.save_timer.start(self.save_timer_interval_ms)  # 每10分钟保存数据

            self.export_relay_log_btn.setEnabled(True)
            self.export_status_log_btn.setEnabled(True)
            self.export_temp_humi_btn.setEnabled(True)

        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            self.connection_status.setText("🔴 连接失败")
            self.connection_status.setStyleSheet("color: red; font-weight: bold;")
            self.modbus_client = None
            self.set_controls_enabled(False)
            self.export_relay_log_btn.setEnabled(False)
            self.export_status_log_btn.setEnabled(False)
            self.export_temp_humi_btn.setEnabled(False)

    def disconnect_all(self):
        if self.modbus_client:
            try:
                self.modbus_client.close()
            except:
                pass
            self.modbus_client = None

        self.status_label.setText("状态：未连接")
        self.connection_status.setText("🔴 未连接")
        self.connection_status.setStyleSheet("color: red; font-weight: bold;")
        self.connect_btn.setText("连接串口")
        self.set_controls_enabled(False)

        # 停止所有定时器
        self.poll_timer.stop()
        self.status_timer.stop()
        self.save_timer.stop()

        self.log("⚠️ 串口断开连接")
        self.export_relay_log_btn.setEnabled(False)
        self.export_status_log_btn.setEnabled(False)
        self.export_temp_humi_btn.setEnabled(False)

    def set_controls_enabled(self, enabled):
        for btn in self.do_buttons:
            btn.setEnabled(enabled)
        for chk in self.auto_checkboxes:
            chk.setEnabled(enabled)
        for st in self.start_time_edits:
            st.setEnabled(enabled)
        for et in self.end_time_edits:
            et.setEnabled(enabled)
        self.export_relay_log_btn.setEnabled(enabled)
        self.export_status_log_btn.setEnabled(enabled)
        self.export_temp_humi_btn.setEnabled(enabled)

    def manual_toggle_do(self, idx, checked):
        if not self.modbus_client:
            self.log("❌ 串口未连接，无法操作继电器")
            self.do_buttons[idx].setChecked(not checked)
            return

        try:
            result = self.modbus_client.write_coil(address=idx, value=checked, unit=self.relay_slave_id)
            if result.isError():
                raise Exception("写入失败")
            self.do_buttons[idx].setText("闭合" if checked else "断开")
            self.log(f"DO{idx + 1} 手动 {'闭合' if checked else '断开'} 成功")
            self.record_relay_action(idx + 1, "手动闭合" if checked else "手动断开")
        except Exception as e:
            self.log(f"❌ DO{idx + 1} 手动 {'闭合' if checked else '断开'} 失败: {e}")
            self.do_buttons[idx].setChecked(not checked)

    def poll_devices(self):
        if self.poll_flag:
            self.read_temp_humi()
        else:
            self.refresh_relay_status()
            self.schedule_relay_control_check()
        self.poll_flag = not self.poll_flag

    def read_temp_humi(self):
        if not self.modbus_client:
            return
        try:
            rr = self.modbus_client.read_holding_registers(address=0, count=2, unit=self.sensor_slave_id)
            if rr.isError():
                raise Exception("读取温湿度失败")
            humidity = rr.registers[0] / 10.0
            temperature = rr.registers[1] / 10.0

            # 更新显示
            self.temp_label.setText(f"🌡️ 温度: {temperature:.1f} ℃")
            self.humi_label.setText(f"💧 湿度: {humidity:.1f} %RH")

            # 存储到内存中，等待定时保存
            now = datetime.now()
            self.temp_humi_data.append({
                'timestamp': now,
                'temperature': temperature,
                'humidity': humidity
            })

            # 限制内存中数据量，保留最近1000条记录
            if len(self.temp_humi_data) > 1000:
                self.temp_humi_data = self.temp_humi_data[-1000:]

            # 不再每次都写入文件，改为定时批量保存
            # self.log(f"🌡 温度: {temperature:.1f} ℃, 湿度: {humidity:.1f} %RH")
        except Exception as e:
            self.temp_label.setText("🌡️ 温度: 错误")
            self.humi_label.setText("💧 湿度: 错误")
            self.log(f"❌ 读取温湿度异常: {e}")

    def refresh_relay_status(self):
        if not self.modbus_client:
            return
        try:
            result = self.modbus_client.read_coils(address=0, count=4, unit=self.relay_slave_id)
            if result.isError():
                raise Exception("读取继电器状态失败")
            statuses = result.bits
            for i in range(min(4, len(statuses))):
                old_status = self.do_buttons[i].isChecked()
                new_status = statuses[i]
                if old_status != new_status:
                    action_str = "自动闭合" if new_status else "自动断开"
                    self.record_relay_action(i + 1, action_str)
                self.do_buttons[i].blockSignals(True)
                self.do_buttons[i].setChecked(new_status)
                self.do_buttons[i].setText("闭合" if new_status else "断开")
                self.do_buttons[i].blockSignals(False)
        except Exception as e:
            self.log(f"❌ 读取继电器状态失败: {e}")

    def schedule_relay_control_check(self):
        if not self.modbus_client:
            return
        now = datetime.now().time()
        for i in range(4):
            if self.auto_checkboxes[i].isChecked():
                start = self.start_time_edits[i].time().toPyTime()
                end = self.end_time_edits[i].time().toPyTime()
                in_period = start <= now <= end if start <= end else now >= start or now <= end
                try:
                    self.modbus_client.write_coil(address=i, value=in_period, unit=self.relay_slave_id)
                    self.log(f"🕒 DO{i + 1} 时间段控制 {'闭合' if in_period else '断开'}")
                except Exception as e:
                    self.log(f"❌ DO{i + 1} 时间段控制异常: {e}")

    def save_data_to_file(self):
        """定时保存数据到文件（每10分钟执行一次）"""
        if not self.temp_humi_data or not self.save_path:
            return

        try:
            # 检查是否需要创建新的日期文件夹
            today = datetime.now().strftime("%Y-%m-%d")
            current_folder = os.path.basename(self.save_path)
            if current_folder != today:
                self.create_daily_folder()
                self.last_save_label.setText("📁 保存目录: " + self.save_path)

            # 保存温湿度数据
            temp_humi_file = os.path.join(self.save_path, "温湿度记录.csv")
            file_exists = os.path.exists(temp_humi_file)

            with open(temp_humi_file, mode='a', newline='', encoding='utf-8-sig') as file:
                writer = csv.writer(file)
                if not file_exists:
                    writer.writerow(["时间", "温度(℃)", "湿度(%RH)"])

                # 写入所有未保存的数据
                for data in self.temp_humi_data:
                    timestamp_str = data['timestamp'].strftime("%Y-%m-%d %H:%M:%S")
                    writer.writerow([timestamp_str, f"{data['temperature']:.1f}", f"{data['humidity']:.1f}"])

            # 保存设备运行状态
            self.save_device_status()

            # 保存继电器状态
            self.save_relay_status()

            data_count = len(self.temp_humi_data)
            self.temp_humi_data.clear()  # 清空已保存的数据

            # 更新下次保存时间显示
            next_save_time = datetime.now().strftime("%H:%M:%S")
            self.next_save_label.setText(f"⏰ 上次保存: {next_save_time} ({data_count}条数据)")

            self.log(f"� 定时保存完成: {data_count}条温湿度数据")

        except Exception as e:
            self.log(f"❌ 保存数据失败: {e}")

    def save_device_status(self):
        """保存设备运行状态"""
        try:
            status_file = os.path.join(self.save_path, "设备运行状态.csv")
            file_exists = os.path.exists(status_file)

            with open(status_file, mode='a', newline='', encoding='utf-8-sig') as file:
                writer = csv.writer(file)
                if not file_exists:
                    writer.writerow(["时间", "连接状态", "温湿度传感器", "继电器控制器"])

                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                connected = self.modbus_client is not None
                temp_status = "正常" if "错误" not in self.temp_label.text() else "异常"
                relay_status = "正常" if connected else "异常"

                writer.writerow([now, "正常" if connected else "异常", temp_status, relay_status])

        except Exception as e:
            self.log(f"❌ 保存设备状态失败: {e}")

    def save_relay_status(self):
        """保存继电器当前状态"""
        try:
            relay_file = os.path.join(self.save_path, "继电器状态记录.csv")
            file_exists = os.path.exists(relay_file)

            with open(relay_file, mode='a', newline='', encoding='utf-8-sig') as file:
                writer = csv.writer(file)
                if not file_exists:
                    writer.writerow(["时间", "DO1", "DO2", "DO3", "DO4"])

                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                relay_states = []
                for i in range(4):
                    state = "闭合" if self.do_buttons[i].isChecked() else "断开"
                    relay_states.append(state)

                writer.writerow([now] + relay_states)

        except Exception as e:
            self.log(f"❌ 保存继电器状态失败: {e}")

    def record_relay_action(self, do_num, action):
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.relay_action_log.append((now, f"DO{do_num}", action))
        self.log(f"📝 记录继电器动作: DO{do_num} {action}")

    def record_run_status(self):
        connected = self.modbus_client is not None and getattr(self.modbus_client, 'socket', None) is not None
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.run_status_log.append((now, connected))
        self.log(f"📊 运行状态记录: {now} - {'正常' if connected else '异常'}")

    def export_relay_action_log(self):
        if not self.relay_action_log:
            QMessageBox.information(self, "导出日志", "继电器动作日志为空！")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "保存继电器动作日志", "继电器动作日志.csv", "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            with open(file_path, mode='w', newline='', encoding='utf-8-sig') as file:
                writer = csv.writer(file)
                writer.writerow(["时间", "继电器", "动作"])
                writer.writerows(self.relay_action_log)
            self.log(f"✅ 继电器动作日志已导出: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出继电器动作日志失败: {e}")

    def export_run_status_log(self):
        if not self.run_status_log:
            QMessageBox.information(self, "导出日志", "运行状态日志为空！")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "保存运行状态日志", "运行状态日志.csv", "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            with open(file_path, mode='w', newline='', encoding='utf-8-sig') as file:
                writer = csv.writer(file)
                writer.writerow(["时间", "状态"])
                for time_str, status in self.run_status_log:
                    writer.writerow([time_str, "正常" if status else "异常"])
            self.log(f"✅ 运行状态日志已导出: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出运行状态日志失败: {e}")

    def export_temp_humi_data(self):
        """导出温湿度数据"""
        if not self.save_path:
            QMessageBox.information(self, "导出数据", "没有可导出的温湿度数据！")
            return

        temp_humi_file = os.path.join(self.save_path, "温湿度记录.csv")
        if not os.path.exists(temp_humi_file):
            QMessageBox.information(self, "导出数据", "温湿度记录文件不存在！")
            return

        file_path, _ = QFileDialog.getSaveFileName(self, "导出温湿度数据", "温湿度数据导出.csv", "CSV Files (*.csv)")
        if not file_path:
            return

        try:
            # 复制文件
            import shutil
            shutil.copy2(temp_humi_file, file_path)
            self.log(f"✅ 温湿度数据已导出: {file_path}")
            QMessageBox.information(self, "导出成功", f"温湿度数据已成功导出到:\n{file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出温湿度数据失败: {e}")
            self.log(f"❌ 导出温湿度数据失败: {e}")

    def log(self, msg):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {msg}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())
