from PIL import Image
import os
import cv2
import numpy as np

def read_image_unicode(path):
    with Image.open(path) as img:
        img = img.convert('L')
        return np.array(img)

# ---------- 计算两张图之间的上下裁剪量 ----------
def compute_crop_between_two_images(img_path1, img_path2):
    sift = cv2.SIFT_create()

    img1 = read_image_unicode(img_path1)
    img2 = read_image_unicode(img_path2)

    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)

    if des1 is None or des2 is None:
        return 210, 100  # 默认值

    bf = cv2.BFMatcher()
    matches = bf.knnMatch(des1, des2, k=2)
    good_matches = [m for m, n in matches if m.distance < 0.75 * n.distance]

    if len(good_matches) < 5:
        return 210, 100

    dy_list = [kp1[m.queryIdx].pt[1] - kp2[m.trainIdx].pt[1] for m in good_matches]
    dy_median = np.median(dy_list)

    x_crop = int(max(0, dy_median / 2))   # 上裁
    y_crop = int(max(0, -dy_median / 2))  # 下裁
    return x_crop, y_crop


# ---------- 主程序 ----------
width_crop = 1608
group_size = 7

input_directory = r'G:/大满站数据/0704/scan_0704_221357/yangdai/photo_3D_80ns/photo_3D_80ns_crop'
output_directory = os.path.join(input_directory, 'output')
os.makedirs(output_directory, exist_ok=True)

bmp_files = sorted([f for f in os.listdir(input_directory) if f.endswith('.bmp')],
                   key=lambda x: int(os.path.splitext(x)[0]))

total_images = len(bmp_files)
total_groups = (total_images + group_size - 1) // group_size

for group_number in range(total_groups):
    start_idx = group_number * group_size
    end_idx = min(start_idx + group_size, total_images)
    group_files = bmp_files[start_idx:end_idx]
    group_paths = [os.path.join(input_directory, f) for f in group_files]

    print(f"\n处理样带 {group_number + 1}，图像数量：{len(group_paths)}")

    # 每张图对应一个裁剪量，最后一张用上一张的裁剪
    crop_params = []
    for i in range(len(group_paths)):
        if i < len(group_paths) - 1:
            x_crop, y_crop = compute_crop_between_two_images(group_paths[i], group_paths[i + 1])
        else:
            x_crop, y_crop = crop_params[-1] if crop_params else (210, 100)
        crop_params.append((x_crop, y_crop))
        print(f"图像 {os.path.basename(group_paths[i])} ：x_crop={x_crop}, y_crop={y_crop}")

    # 裁剪图像
    cropped_images = []
    for i, img_path in enumerate(group_paths):
        img = Image.open(img_path)
        x_crop, y_crop = crop_params[i]
        cropped_img = img.crop((0, x_crop, width_crop, img.height - y_crop))
        cropped_images.append(cropped_img)

    # 拼接图像
    total_height = sum(img.height for img in cropped_images)
    final_width = cropped_images[0].width
    final_image = Image.new('RGB', (final_width, total_height))

    current_y = 0
    if (group_number + 1) % 2 == 0:
        for img in reversed(cropped_images):
            final_image.paste(img, (0, current_y))
            current_y += img.height
    else:
        for img in cropped_images:
            final_image.paste(img, (0, current_y))
            current_y += img.height

    output_image_path = os.path.join(output_directory, f'样带{group_number + 1}.bmp')
    final_image.save(output_image_path)
    print(f"样带 {group_number + 1} 已保存：{output_image_path}")
