import os
import shutil

# 设置源目录和目标保存目录
source_root_dir = r'F:/daman/0709/scan_0709_102453/yangdai'     # 样本根目录
destination_dir = r'F:/daman/0709/scan_0709_102453/yangdai/photo_RGB'  # 实际的目标目录
os.makedirs(destination_dir, exist_ok=True)  # 创建保存目录

# 初始化图片计数器
image_counter = 1

# ----------遍历 "样带1" 到 "样带27"-------------
for i in range(1, 40):
    sample_folder = f'样带{i}'
    sample_path = os.path.join(source_root_dir, sample_folder)

    if os.path.isdir(sample_path):
        # 获取子文件夹并按数字名称排序
        sub_folders = sorted(
            [f for f in os.listdir(sample_path) if os.path.isdir(os.path.join(sample_path, f))],
            key=lambda x: int(x) if x.isdigit() else float('inf')
        )

        for sub_folder in sub_folders:
            sub_folder_path = os.path.join(sample_path, sub_folder)

            # 构建 "75.00 ns\res_bmp" 路径
            res_bmp_path = os.path.join(sub_folder_path, '100.00 ns', 'ori_bmp')

            if os.path.exists(res_bmp_path):
                # 获取图片并排序
                images = sorted([f for f in os.listdir(res_bmp_path) if f.endswith(('.bmp', '.png', '.jpg'))])

                if images:
                    first_image = images[1]  # 获取第6张图片
                    source_image_path = os.path.join(res_bmp_path, first_image)

                    # 使用当前子文件夹的名称作为目标图片名（如 1.bmp, 2.bmp, ..., 10.bmp）
                    destination_image_path = os.path.join(destination_dir, f'{sub_folder}.bmp')

                    shutil.copyfile(source_image_path, destination_image_path)
                    print(f"已提取: {source_image_path} -> 保存为: {destination_image_path}")

print("所有图片提取并保存完成")
