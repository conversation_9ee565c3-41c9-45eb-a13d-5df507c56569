# from PIL import Image
# import os

# # 设置裁剪参数
# width_crop = 1608  # 水平方向保留的像素宽度

# # 定义输入和输出路径
# input_directory = 'F:/daman/0704/scan_0704_224745/yangdai/photo_3D_80ns'
# output_directory = 'F:/daman/0704/scan_0704_224745/yangdai/photo_3D_80ns_crop'

# # 确保输出目录存在
# os.makedirs(output_directory, exist_ok=True)

# # 获取当前文件夹下所有 .bmp 文件，并按文件名从小到大排序
# bmp_files = sorted([f for f in os.listdir(input_directory) if f.endswith('.bmp')], key=lambda x: int(x.split('.')[0]))

# # 循环处理每个 .bmp 文件
# for img_file in bmp_files:
#     img_path = os.path.join(input_directory, img_file)
#     img = Image.open(img_path)

#     # 裁剪图片右边，保留 width_crop 个像素宽度
#     cropped_img = img.crop((0, 0, width_crop, img.height))

#     # 生成输出文件路径
#     output_image = os.path.join(output_directory, img_file)

#     # 保存裁剪后的图像
#     cropped_img.save(output_image)

#     print(f"{img_file} 已裁剪并保存至 {output_image}")
from PIL import Image
import os

# ------------------ 裁剪参数设置 ------------------
# 单位：像素
left_crop = 212    # 左边裁剪像素
right_crop = 312   # 右边裁剪像素
top_crop = 0      # 上边裁剪像素
bottom_crop = 0   # 下边裁剪像素

# ------------------ 输入输出路径 ------------------
input_root = 'D:/daima/gitup/7month/image-stitching-main/testdata2'
output_root = 'D:/daima/gitup/7month/image-stitching-main/testdata2_crop'

# ------------------ 遍历所有子文件夹 ------------------
for root, dirs, files in os.walk(input_root):
    # 相对路径部分
    relative_path = os.path.relpath(root, input_root)
    # 构建输出路径
    output_dir = os.path.join(output_root, relative_path)
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个 .bmp 图像文件
    bmp_files = sorted([f for f in files if f.endswith('.bmp')],
                       key=lambda x: int(os.path.splitext(x)[0]))

    for img_file in bmp_files:
        input_path = os.path.join(root, img_file)
        output_path = os.path.join(output_dir, img_file)

        # 打开图像
        img = Image.open(input_path)
        width, height = img.size

        # 计算裁剪区域（防止越界）
        left = min(max(0, left_crop), width - 1)
        right = max(min(width - right_crop, width), left + 1)
        top = min(max(0, top_crop), height - 1)
        bottom = max(min(height - bottom_crop, height), top + 1)

        # 裁剪
        cropped_img = img.crop((left, top, right, bottom))
        cropped_img.save(output_path)

        print(f"{input_path} 裁剪并保存至 {output_path}")
