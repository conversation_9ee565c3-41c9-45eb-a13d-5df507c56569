import sys
from PyQt5.QtWidgets import (
    <PERSON>Application, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QMessageBox
)
from PyQt5.QtCore import QTimer
from pymodbus.client.sync import ModbusSerialClient


class DOControlWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DO继电器控制面板")
        self.client = None
        self.slave_id = 0x11

        layout = QVBoxLayout()

        # 串口号输入和连接按钮
        h_layout = QHBoxLayout()
        self.port_input = QLineEdit("COM3")
        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        h_layout.addWidget(QLabel("串口号:"))
        h_layout.addWidget(self.port_input)
        h_layout.addWidget(self.connect_btn)
        layout.addLayout(h_layout)

        # DO开关按钮和状态标签
        self.do_buttons = []
        self.do_status_labels = []
        for i in range(4):
            h = QHBoxLayout()
            btn_on = QPushButton(f"DO{i+1} 开")
            btn_off = QPushButton(f"DO{i+1} 关")
            status = QLabel("状态：未知")
            btn_on.setEnabled(False)
            btn_off.setEnabled(False)
            btn_on.clicked.connect(lambda _, idx=i: self.write_do(idx, True))
            btn_off.clicked.connect(lambda _, idx=i: self.write_do(idx, False))
            h.addWidget(btn_on)
            h.addWidget(btn_off)
            h.addWidget(status)
            layout.addLayout(h)
            self.do_buttons.append((btn_on, btn_off))
            self.do_status_labels.append(status)

        self.setLayout(layout)

        # 定时刷新DO状态
        self.timer = QTimer()
        self.timer.timeout.connect(self.read_do_status)
        self.timer.setInterval(2000)  # 2秒刷新一次

    def toggle_connection(self):
        if self.client:
            try:
                self.client.close()
            except Exception:
                pass
            self.client = None
            self.connect_btn.setText("连接串口")
            self.set_do_buttons_enabled(False)
            self.update_status_labels(["未知"] * 4)
            self.timer.stop()
            self.show_message("已断开串口连接")
        else:
            port = self.port_input.text().strip()
            try:
                self.client = ModbusSerialClient(
                    method='rtu',
                    port=port,
                    baudrate=9600,
                    timeout=1,
                    parity='N',
                    stopbits=1,
                    bytesize=8
                )
                if self.client.connect():
                    self.connect_btn.setText("断开连接")
                    self.set_do_buttons_enabled(True)
                    self.show_message(f"串口 {port} 连接成功")
                    self.read_do_status()
                    self.timer.start()
                else:
                    self.client = None
                    self.show_message(f"串口 {port} 连接失败，请检查设备或端口")
            except Exception as e:
                self.client = None
                self.show_message(f"连接异常: {e}")

    def set_do_buttons_enabled(self, enabled: bool):
        for btn_on, btn_off in self.do_buttons:
            btn_on.setEnabled(enabled)
            btn_off.setEnabled(enabled)

    def write_do(self, index, value):
        if not self.client:
            self.show_message("请先连接串口")
            return
        try:
            result = self.client.write_coil(address=index, value=value, unit=self.slave_id)
            if not result.isError():
                self.show_message(f"DO{index + 1} {'闭合' if value else '断开'}成功")
                self.read_do_status()
            else:
                self.show_message(f"写入 DO{index + 1} 失败")
        except Exception as e:
            self.show_message(f"异常: {e}")

    def read_do_status(self):
        if not self.client:
            self.update_status_labels(["未知"] * 4)
            return
        try:
            result = self.client.read_coils(address=0, count=4, unit=self.slave_id)
            if not result.isError():
                statuses = result.bits
                self.update_status_labels(["闭合" if s else "断开" for s in statuses])
            else:
                self.update_status_labels(["错误"] * 4)
        except Exception as e:
            self.update_status_labels(["异常"] * 4)

    def update_status_labels(self, states):
        for label, state in zip(self.do_status_labels, states):
            label.setText(f"状态：{state}")

    def show_message(self, msg):
        QMessageBox.information(self, "提示", msg)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    w = DOControlWidget()
    w.show()
    sys.exit(app.exec_())
