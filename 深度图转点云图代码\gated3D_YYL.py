import cv2
import numpy as np

# src1 和 src2：输入的两张图像（深度或强度图）。
# delay 和 gw：控制距离计算的参数。
# colormap：应用的色图，尽管在当前版本中未启用。
# lower_thresh 和 upper_thresh：阈值，用于图像的预处理，限制图像中的值范围。
# trim：布尔值，决定是否进行距离修剪。
# dist_mat：返回的距离矩阵。
# d_min 和 d_max：返回的最小值和最大值
def gated3D_v2(src1, src2, delay, gw, colormap, lower_thresh, upper_thresh, trim, dist_mat=None, d_min=None,
               d_max=None):
    BARWIDTH = 104
    BARHEIGHT = src1.shape[0]
    IMAGEWIDTH = src1.shape[1]
    IMAGEHEIGHT = src1.shape[0]

    # 光速常数
    c = 3e8      # m/s

    max_val = 0
    min_val = 65535

    # 图像深度
    image_depth = (src1.dtype.itemsize // 2 + 1) * 8    #计算图像的深度，即图像每个像素的位数

    # 阈值处理
    _, temp1 = cv2.threshold(src1, lower_thresh * (1 << image_depth), 0, cv2.THRESH_TOZERO)
    _, temp1 = cv2.threshold(temp1, upper_thresh * (1 << image_depth), 0, cv2.THRESH_TOZERO_INV)

    _, temp2 = cv2.threshold(src2, lower_thresh * (1 << image_depth), 0, cv2.THRESH_TOZERO)
    _, temp2 = cv2.threshold(temp2, upper_thresh * (1 << image_depth), 0, cv2.THRESH_TOZERO_INV)

    mask = np.bitwise_and(temp1.astype(np.uint8), temp2.astype(np.uint8))  # 掩码

    # 转换为float32类型进行计算
    temp2 = temp2.astype(np.float32)

    # 距离计算
    range_mat = (temp1 / temp2 + 1)
    range_mat = delay * 1e-9 * c / 2 + gw * 1e-9 * c / 2 / (range_mat + 1)

    if trim:
        # 计算统计值并进行修剪
        range_mat[~mask.astype(bool)] = 0
        mean = np.mean(range_mat[mask.astype(bool)])
        stddev = np.std(range_mat[mask.astype(bool)])

        range_mat[range_mat < (mean - 2 * stddev)] = 0
        range_mat[range_mat > (mean + 2 * stddev)] = 0

        mask = (range_mat > 0).astype(np.uint8)

    # 记录距离矩阵
    if dist_mat is not None:
        dist_mat[:] = range_mat

    # 最小值和最大值
    min_val = np.min(range_mat[mask.astype(bool)])
    max_val = np.max(range_mat[mask.astype(bool)])

    # 归一化为灰度图像
    img_3d_gray = cv2.normalize(range_mat, None, 0, 255, cv2.NORM_MINMAX)
    img_3d_gray = img_3d_gray.astype(np.uint8)

    # 创建灰度条
    gray_bar = np.ones((BARHEIGHT, BARWIDTH), dtype=np.uint8) * 255
    color_step = BARHEIGHT // 254
    gap = (BARHEIGHT - 254 * color_step) // 2
    for i in range(gap, BARHEIGHT - gap, color_step):
        gray_bar[i:i + color_step, :] = 255 - (i - gap) * 255 // (BARHEIGHT - 2 * gap)

    img_3d_gray = np.hstack((img_3d_gray, gray_bar))
    mask = np.hstack((mask, gray_bar))

    # 可选的色图
    # img_3d = cv2.applyColorMap(img_3d_gray, colormap)
    # res = cv2.bitwise_and(img_3d, img_3d, mask=mask)
    res = cv2.bitwise_and(img_3d_gray, img_3d_gray, mask=mask)

    # 叠加文本
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(res, f"{min_val:.2f}", (IMAGEWIDTH, 20), font, 0.77, (0, 0, 0), 2)
    cv2.putText(res, f"{(min_val + max_val) / 2:.2f}", (IMAGEWIDTH, IMAGEHEIGHT // 2 - 15), font, 0.77, (0, 0, 0), 2)
    cv2.putText(res, f"{max_val:.2f}", (IMAGEWIDTH, IMAGEHEIGHT - 10), font, 0.77, (0, 0, 0), 2)

    if d_min is not None:
        d_min[0] = min_val
    if d_max is not None:
        d_max[0] = max_val

    return res
