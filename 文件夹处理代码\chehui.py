import os
import shutil

# 设置你的主目录路径（原始路径）
main_dir = r"F:/daman/0704/scan_0704_224745/yangdai"

# 遍历样带1 到 样带34 的文件夹
for i in range(1, 35):  # 1 到 34
    sample_dir = os.path.join(main_dir, f"样带{i}")
    if not os.path.exists(sample_dir):
        print(f"{sample_dir} 不存在，跳过")
        continue

    # 遍历样带内的文件夹
    for folder in os.listdir(sample_dir):
        src = os.path.join(sample_dir, folder)
        dst = os.path.join(main_dir, folder)
        if os.path.isdir(src):
            try:
                shutil.move(src, dst)
                print(f"{folder} 已移回主目录")
            except Exception as e:
                print(f"移动 {folder} 时出错：{e}")

    # 可选：删除空的样带目录
    try:
        os.rmdir(sample_dir)
        print(f"{sample_dir} 已删除")
    except OSError:
        print(f"{sample_dir} 不是空目录，未删除")

print("撤回完成！")
