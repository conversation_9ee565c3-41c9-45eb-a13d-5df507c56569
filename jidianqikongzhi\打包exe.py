#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动控制程序打包脚本
使用PyInstaller将Python程序打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['自动控制程序.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('使用说明.md', '.'),
    ],
    hiddenimports=[
        'serial.tools.list_ports',
        'pymodbus.client.sync',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='自动继电器温湿度监控系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version_file=None,
)
'''
    
    with open('自动控制程序.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建 PyInstaller 配置文件: 自动控制程序.spec")

def build_exe():
    """使用PyInstaller打包exe文件"""
    print("🚀 开始打包exe文件...")
    print("=" * 50)
    
    try:
        # 运行PyInstaller
        cmd = ['pyinstaller', '--clean', '自动控制程序.spec']
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        
        print("✅ 打包成功！")
        print(f"📁 exe文件位置: {os.path.abspath('dist')}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 打包过程中发生错误: {e}")
        return False

def organize_output():
    """整理输出文件"""
    print("\n📦 整理输出文件...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return
    
    exe_file = dist_dir / '自动继电器温湿度监控系统.exe'
    if exe_file.exists():
        print(f"✅ exe文件已生成: {exe_file}")
        print(f"📏 文件大小: {exe_file.stat().st_size / (1024*1024):.1f} MB")
        
        # 复制使用说明到dist目录
        try:
            shutil.copy2('使用说明.md', dist_dir)
            print("✅ 已复制使用说明文档到输出目录")
        except Exception as e:
            print(f"⚠️ 复制使用说明失败: {e}")
        
        # 创建启动批处理文件
        bat_content = '''@echo off
echo ========================================
echo    自动继电器和温湿度监控系统
echo ========================================
echo.
echo 正在启动程序...
echo.

start "" "自动继电器温湿度监控系统.exe"
'''
        
        bat_file = dist_dir / '启动程序.bat'
        with open(bat_file, 'w', encoding='gbk') as f:
            f.write(bat_content)
        print("✅ 已创建启动批处理文件")
        
    else:
        print("❌ 未找到生成的exe文件")

def clean_build_files():
    """清理构建文件"""
    print("\n🧹 清理构建文件...")
    
    dirs_to_clean = ['build', '__pycache__']
    files_to_clean = ['自动控制程序.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ 已删除: {dir_name}")
            except Exception as e:
                print(f"⚠️ 删除 {dir_name} 失败: {e}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✅ 已删除: {file_name}")
            except Exception as e:
                print(f"⚠️ 删除 {file_name} 失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("    自动继电器温湿度监控系统 - exe打包工具")
    print("=" * 60)
    print()
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    # 检查必要文件
    required_files = ['自动控制程序.py', '使用说明.md']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在正确的目录中运行此脚本")
        input("按回车键退出...")
        return
    
    print("✅ 所有必要文件都存在")
    print()
    
    # 创建spec文件
    create_spec_file()
    print()
    
    # 打包exe
    if build_exe():
        organize_output()
        print()
        print("🎉 打包完成！")
        print("=" * 50)
        print("📁 输出目录: dist/")
        print("🚀 可执行文件: dist/自动继电器温湿度监控系统.exe")
        print("📖 使用说明: dist/使用说明.md")
        print("🔧 启动脚本: dist/启动程序.bat")
        print()
        
        # 询问是否清理构建文件
        choice = input("是否清理构建文件？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            clean_build_files()
        
        print("\n✅ 所有操作完成！")
        
    else:
        print("❌ 打包失败，请检查错误信息")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
