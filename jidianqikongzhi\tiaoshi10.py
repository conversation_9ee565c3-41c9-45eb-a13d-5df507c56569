import sys
import csv
from datetime import datetime, time as dtime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, QLineEdit,
    QMessageBox, QGroupBox, QTextEdit, QTimeEdit, QGridLayout, QCheckBox, QFileDialog
)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, QTime
from pymodbus.client.sync import ModbusSerialClient as ModbusClient
from pymodbus.exceptions import ModbusIOException

# --- 1. 定义Modbus通信线程 ---
class ModbusWorker(QThread):
    # 定义信号，用于向主线程发送数据和状态
    connection_status = pyqtSignal(bool, str) # 连接状态 (True/False, 错误信息)
    temperature_humidity_data = pyqtSignal(float, float) # 温湿度数据 (温度, 湿度)
    relay_status_read = pyqtSignal(list) # 继电器状态列表 (bool列表)
    error_occurred = pyqtSignal(str) # 错误信息
    relay_action_success = pyqtSignal(int, bool) # 继电器操作成功 (DO编号, 状态)

    def __init__(self, port, relay_id, sensor_id):
        super().__init__()
        self.port = port
        self.relay_slave_id = relay_id
        self.sensor_slave_id = sensor_id
        self.client = None
        self.running = True
        self.poll_flag = True # True 读温湿度，False 读继电器和执行定时控制

        # 继电器定时控制参数，由主线程设置
        self.auto_control_settings = [] # 格式：[(bool, QTime, QTime), ...]

    def run(self):
        try:
            self.client = ModbusClient(method='rtu', port=self.port, baudrate=9600, timeout=1)
            if not self.client.connect():
                self.connection_status.emit(False, "Modbus 设备连接失败或串口被占用")
                self.client = None
                return

            self.connection_status.emit(True, f"已连接 {self.port}")
            
            # 初始化继电器为断开状态，避免未知状态
            for i in range(4):
                try:
                    self.client.write_coil(address=i, value=False, unit=self.relay_slave_id)
                except ModbusIOException:
                    # 首次连接时如果继电器模块未响应，尝试设为断开可能失败，忽略
                    pass

            while self.running:
                if self.client and self.client.connected:
                    try:
                        if self.poll_flag:
                            self._read_temp_humi_threaded()
                        else:
                            self._refresh_relay_status_threaded()
                            self._schedule_relay_control_check_threaded()
                        self.poll_flag = not self.poll_flag
                    except ModbusIOException as e:
                        self.error_occurred.emit(f"Modbus通信异常: {e}")
                        # 如果出现Modbus通信异常，尝试重新连接
                        if self.client:
                            self.client.close()
                            self.client = None
                        self.connection_status.emit(False, "Modbus通信异常，尝试重新连接...")
                        # 暂停一段时间再尝试，避免频繁重试
                        QThread.sleep(3)
                        try:
                            self.client = ModbusClient(method='rtu', port=self.port, baudrate=9600, timeout=1)
                            if not self.client.connect():
                                self.connection_status.emit(False, f"重新连接失败: {self.port}")
                                self.client = None
                                return
                            else:
                                self.connection_status.emit(True, f"重新连接成功: {self.port}")
                        except Exception as reconnect_e:
                            self.connection_status.emit(False, f"重新连接失败: {reconnect_e}")
                            self.client = None
                            return
                    except Exception as e:
                        self.error_occurred.emit(f"未知异常: {e}")
                QThread.msleep(500) # 轮询间隔，500ms
        finally:
            if self.client:
                try:
                    self.client.close()
                except Exception as e:
                    self.error_occurred.emit(f"关闭Modbus客户端异常: {e}")
            self.connection_status.emit(False, "已断开连接")


    def stop(self):
        self.running = False
        self.quit()
        self.wait()

    def _read_temp_humi_threaded(self):
        if not self.client or not self.client.connected:
            return
        rr = self.client.read_holding_registers(address=0, count=2, unit=self.sensor_slave_id)
        if rr.isError():
            raise ModbusIOException(f"读取温湿度失败: {rr}")
        
        humidity_raw = rr.registers[0]
        temp_raw = rr.registers[1]
        humidity = humidity_raw / 10.0
        temperature = temp_raw / 10.0
        self.temperature_humidity_data.emit(temperature, humidity)

    def _refresh_relay_status_threaded(self):
        if not self.client or not self.client.connected:
            return
        result = self.client.read_coils(address=0, count=4, unit=self.relay_slave_id)
        if result.isError():
            raise ModbusIOException(f"读取继电器状态失败: {result}")
        self.relay_status_read.emit(result.bits[:4]) # 确保只发送前4个继电器状态

    def _schedule_relay_control_check_threaded(self):
        if not self.client or not self.client.connected:
            return
        now = datetime.now().time()
        for i in range(4):
            if i < len(self.auto_control_settings): # 检查索引是否有效
                auto_enabled, start_time_q, end_time_q = self.auto_control_settings[i]
                if auto_enabled:
                    start = start_time_q.toPyTime()
                    end = end_time_q.toPyTime()
                    
                    in_period = False
                    if start <= end:
                        in_period = start <= now <= end
                    else:
                        in_period = now >= start or now <= end  # 跨天处理

                    # 注意：这里我们只尝试写入，具体的日志记录和状态刷新由主线程的refresh_relay_status_read信号处理
                    # 避免在子线程频繁操作UI
                    try:
                        self.client.write_coil(address=i, value=in_period, unit=self.relay_slave_id)
                    except ModbusIOException as e:
                        self.error_occurred.emit(f"DO{i+1} 定时控制写入失败: {e}")
            
    # 从主线程接收手动控制命令
    def write_relay_coil(self, idx, value):
        if not self.client or not self.client.connected:
            self.error_occurred.emit("Modbus客户端未连接或已断开，无法操作继电器。")
            return
        try:
            result = self.client.write_coil(address=idx, value=value, unit=self.relay_slave_id)
            if result.isError():
                raise ModbusIOException(f"DO{idx+1} 写入失败: {result}")
            self.relay_action_success.emit(idx, value) # 发送操作成功信号
        except ModbusIOException as e:
            self.error_occurred.emit(f"DO{idx+1} 操作异常: {e}")

# --- 2. 主窗口类 ---
class MainWindow(QWidget):
    # 定义一个信号，用于将继电器手动操作命令发送给ModbusWorker
    # 这个信号将从主线程的UI事件触发，被ModbusWorker槽接收
    manual_relay_command = pyqtSignal(int, bool)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("继电器和温湿度传感器综合控制器")
        self.resize(700, 650)

        self.RELAY_SLAVE_ID = 0x11  # 继电器从站ID
        self.SENSOR_SLAVE_ID = 0x01  # 温湿度传感器从站ID
        self.POLLING_INTERVAL_MS = 1000 # 轮询间隔，子线程内部控制
        self.STATUS_LOG_INTERVAL_MS = 10 * 1000 # 运行状态记录间隔

        self.modbus_worker = None
        self.save_data = False
        self.save_path = None

        # 继电器动作日志列表，格式：[(时间, DO编号, 动作, 类型)] (类型: 手动/自动)
        self.relay_action_log = []

        # 运行状态日志列表，格式：[(时间, 是否正常)]
        self.run_status_log = []

        # 记录继电器上次的UI显示状态，用于判断是否真的发生改变（针对定时控制和刷新）
        self.last_relay_ui_states = [False] * 4

        self.init_ui()

        # 运行状态定时记录（每10秒），这个定时器仍然在主线程
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.record_run_status)
        # 在连接串口后启动此定时器
        self.status_timer.setInterval(self.STATUS_LOG_INTERVAL_MS)


    def init_ui(self):
        layout = QVBoxLayout()

        # --- 串口连接区域 ---
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("串口号:"))
        self.port_input = QLineEdit("COM7")
        port_layout.addWidget(self.port_input)

        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        port_layout.addWidget(self.connect_btn)

        self.status_label = QLabel("状态：未连接")
        port_layout.addWidget(self.status_label)
        layout.addLayout(port_layout)

        # --- DO继电器控制区域 ---
        relay_group = QGroupBox("DO继电器控制")
        relay_layout = QGridLayout()

        self.do_buttons = []
        self.auto_checkboxes = []
        self.start_time_edits = []
        self.end_time_edits = []

        for i in range(4):
            relay_layout.addWidget(QLabel(f"DO{i + 1}"), i, 0)
            btn = QPushButton("断开")
            btn.setCheckable(True)
            # 修改连接方式，手动按钮的点击事件发送命令给worker线程
            btn.clicked.connect(lambda checked, idx=i: self.send_manual_relay_command(idx, checked))
            relay_layout.addWidget(btn, i, 1)
            self.do_buttons.append(btn)

            auto_chk = QCheckBox("定时")
            # 定时复选框的点击事件，用于更新worker线程的定时设置
            auto_chk.clicked.connect(self.update_auto_control_settings)
            relay_layout.addWidget(auto_chk, i, 2)
            self.auto_checkboxes.append(auto_chk)

            relay_layout.addWidget(QLabel("开始时间"), i, 3)
            start_time = QTimeEdit()
            start_time.setDisplayFormat("HH:mm")
            start_time.setTime(QTime(8, 0))
            start_time.timeChanged.connect(self.update_auto_control_settings) # 时间变化也更新设置
            relay_layout.addWidget(start_time, i, 4)
            self.start_time_edits.append(start_time)

            relay_layout.addWidget(QLabel("结束时间"), i, 5)
            end_time = QTimeEdit()
            end_time.setDisplayFormat("HH:mm")
            end_time.setTime(QTime(18, 0))
            end_time.timeChanged.connect(self.update_auto_control_settings) # 时间变化也更新设置
            relay_layout.addWidget(end_time, i, 6)
            self.end_time_edits.append(end_time)

        relay_group.setLayout(relay_layout)
        layout.addWidget(relay_group)

        # --- 温湿度显示区域 ---
        sensor_group = QGroupBox("温湿度传感器")
        sensor_layout = QHBoxLayout()
        self.temp_label = QLabel("温度: -- ℃")
        self.humi_label = QLabel("湿度: -- %RH")
        sensor_layout.addWidget(self.temp_label)
        sensor_layout.addWidget(self.humi_label)
        sensor_group.setLayout(sensor_layout)
        layout.addWidget(sensor_group)

        # 温湿度保存控制
        save_layout = QHBoxLayout()
        self.select_dir_btn = QPushButton("选择保存目录")
        self.select_dir_btn.clicked.connect(self.select_save_directory)
        save_layout.addWidget(self.select_dir_btn)

        self.start_save_btn = QPushButton("开始保存")
        self.start_save_btn.clicked.connect(self.start_saving)
        self.start_save_btn.setEnabled(False)
        save_layout.addWidget(self.start_save_btn)

        self.stop_save_btn = QPushButton("停止保存")
        self.stop_save_btn.clicked.connect(self.stop_saving)
        self.stop_save_btn.setEnabled(False)
        save_layout.addWidget(self.stop_save_btn)

        self.export_relay_log_btn = QPushButton("导出继电器动作日志")
        self.export_relay_log_btn.clicked.connect(self.export_relay_action_log)
        self.export_relay_log_btn.setEnabled(False)
        save_layout.addWidget(self.export_relay_log_btn)

        self.export_status_log_btn = QPushButton("导出运行状态日志")
        self.export_status_log_btn.clicked.connect(self.export_run_status_log)
        self.export_status_log_btn.setEnabled(False)
        save_layout.addWidget(self.export_status_log_btn)

        layout.addLayout(save_layout)

        # --- 日志显示区域 ---
        layout.addWidget(QLabel("日志："))
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        self.setLayout(layout)
        self.set_controls_enabled(False) # 初始状态为禁用

    # --- 槽函数 (Slot Functions) ---
    def toggle_connection(self):
        if self.modbus_worker and self.modbus_worker.isRunning():
            # 停止Modbus工作线程
            self.modbus_worker.stop()
            self.modbus_worker = None
            self.status_timer.stop() # 停止状态记录定时器
            self.set_controls_enabled(False)
            self.status_label.setText("状态：未连接")
            self.connect_btn.setText("连接串口")
            self.log("⚠️ 串口断开连接")
            return

        port_name = self.port_input.text().strip()
        if not port_name:
            QMessageBox.warning(self, "错误", "请输入串口号！")
            return

        self.status_label.setText(f"状态：正在连接 {port_name}...")
        self.connect_btn.setEnabled(False) # 连接过程中禁用按钮，防止重复点击

        # 创建Modbus工作线程并连接信号
        self.modbus_worker = ModbusWorker(port_name, self.RELAY_SLAVE_ID, self.SENSOR_SLAVE_ID)
        self.modbus_worker.connection_status.connect(self.handle_connection_status)
        self.modbus_worker.temperature_humidity_data.connect(self.update_temp_humi_display)
        self.modbus_worker.relay_status_read.connect(self.update_relay_ui_status)
        self.modbus_worker.error_occurred.connect(self.log_error)
        self.modbus_worker.relay_action_success.connect(self.handle_manual_relay_action_success)

        # 将主线程的继电器手动控制信号连接到worker线程的槽
        self.manual_relay_command.connect(self.modbus_worker.write_relay_coil)

        self.modbus_worker.start() # 启动线程

    def handle_connection_status(self, connected, message):
        self.status_label.setText(f"状态：{message}")
        self.connect_btn.setEnabled(True) # 恢复连接按钮
        
        if connected:
            self.connect_btn.setText("断开串口")
            self.set_controls_enabled(True)
            self.log(f"✅ 串口 {message} 连接成功")
            self.status_timer.start() # 连接成功后启动状态记录定时器
            self.update_auto_control_settings() # 连接后立即同步定时设置到worker
        else:
            self.connect_btn.setText("连接串口")
            self.set_controls_enabled(False)
            self.log(f"❌ 连接失败: {message}")
            if self.modbus_worker: # 如果ModbusWorker因连接失败而停止，清理
                self.modbus_worker.stop()
                self.modbus_worker = None
            self.status_timer.stop() # 连接失败停止定时器

    def update_temp_humi_display(self, temperature, humidity):
        self.temp_label.setText(f"温度: {temperature:.1f} ℃")
        self.humi_label.setText(f"湿度: {humidity:.1f} %RH")

        if self.save_data and self.save_path:
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            filename = f"{self.save_path}/温湿度记录.csv"
            try:
                with open(filename, mode='a', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    # 检查文件是否为空，如果是，则写入CSV头部
                    if file.tell() == 0:
                        writer.writerow(["时间", "温度(℃)", "湿度(%RH)"])
                    writer.writerow([now, f"{temperature:.1f}", f"{humidity:.1f}"])
                # self.log(f"🌡 温度: {temperature:.1f} ℃, 湿度: {humidity:.1f} %RH 已保存") # 避免频繁日志
            except Exception as e:
                self.log(f"❌ 保存温湿度数据失败: {e}")
        # 实时显示温度湿度
        self.log(f"🌡 温度: {temperature:.1f} ℃, 湿度: {humidity:.1f} %RH")

    def update_relay_ui_status(self, statuses):
        # 这个槽函数由ModbusWorker线程发送的read_relay_status信号触发
        # 负责更新UI上继电器的按钮状态，并记录非手动引起的继电器动作日志
        for i in range(min(4, len(statuses))):
            new_status = statuses[i]
            current_ui_status = self.do_buttons[i].isChecked()

            # 如果设备上继电器状态与UI显示的（或上次命令的）状态不一致，且不是手动操作的结果
            # 这里的判断需要更精细，通常是设备状态发生变化时才记录
            # 最好的方式是：手动操作时记录“手动”，定时操作时由定时逻辑记录“自动”，
            # 这里的刷新只是同步UI，除非是发现设备状态与任何命令不符，才记录“外部变更”
            if current_ui_status != new_status:
                # 阻止信号，避免setChecked再次触发manual_toggle_do或send_manual_relay_command
                self.do_buttons[i].blockSignals(True)
                self.do_buttons[i].setChecked(new_status)
                self.do_buttons[i].setText("闭合" if new_status else "断开")
                self.do_buttons[i].blockSignals(False)

                # 只有当UI状态和设备实际状态不一致时才记录，且该变化不是由当前UI触发的
                # 但这里难以判断是"定时"还是"外部"改变，目前假设只要UI更新了就记录。
                # 更好的方式是，由worker线程在执行定时控制时，或者主线程收到设备状态变化信号时，
                # 与上一次设定的目标状态比较，如果目标状态与当前UI状态不符，则记录为“自动”或“外部”
                # For simplicity, we log changes detected from device.
                action_type = "自动" if self.auto_checkboxes[i].isChecked() else "外部变更"
                # 进一步优化：实际的继电器定时动作应该由worker线程在成功写入后发信号回来，
                # 这里只负责同步UI和处理可能的手动操作带来的日志。
                # 简化处理：如果UI状态改变了，且不是手动操作直接触发的，就记录。
                # 在此版本中，我们将定时控制的日志也放在这里由主线程记录，因为它是通过read_coils间接感知的。
                if self.last_relay_ui_states[i] != new_status:
                    self.record_relay_action(i + 1, "闭合" if new_status else "断开", action_type)
                    self.last_relay_ui_states[i] = new_status


    def log_error(self, message):
        self.log(f"❌ {message}")
        # 如果是连接断开类的错误，可以考虑自动重置UI状态
        if "Modbus通信异常" in message or "重新连接失败" in message:
            if self.modbus_worker: # 确保工作线程已停止
                self.modbus_worker.stop()
                self.modbus_worker = None
            self.status_timer.stop()
            self.set_controls_enabled(False)
            self.status_label.setText("状态：未连接")
            self.connect_btn.setText("连接串口")
            self.log("⚠️ 因Modbus通信错误，已断开连接")


    def set_controls_enabled(self, enabled):
        # 控制继电器手动按钮在未连接时可以点击，但在连接后才发送命令
        for btn in self.do_buttons:
            btn.setEnabled(enabled)
        for chk in self.auto_checkboxes:
            chk.setEnabled(enabled)
        for st in self.start_time_edits:
            st.setEnabled(enabled)
        for et in self.end_time_edits:
            et.setEnabled(enabled)
        self.select_dir_btn.setEnabled(enabled)
        self.start_save_btn.setEnabled(enabled and self.save_path is not None)
        self.stop_save_btn.setEnabled(enabled and self.save_data) # 只有在保存中才能停止
        self.export_relay_log_btn.setEnabled(enabled)
        self.export_status_log_btn.setEnabled(enabled)

    # 主线程发送手动控制命令给ModbusWorker
    def send_manual_relay_command(self, idx, checked):
        # 立即更新UI状态（假定操作会成功），并通过信号发送到工作线程
        # 阻止信号，防止在发送命令前因setChecked导致递归调用
        self.do_buttons[idx].blockSignals(True)
        self.do_buttons[idx].setChecked(checked)
        self.do_buttons[idx].setText("闭合" if checked else "断开")
        self.do_buttons[idx].blockSignals(False)

        if self.modbus_worker and self.modbus_worker.isRunning():
            self.manual_relay_command.emit(idx, checked)
        else:
            self.log("❌ 串口未连接或工作线程未运行，无法操作继电器")
            # 如果命令发送失败，将UI按钮恢复到原状态
            self.do_buttons[idx].blockSignals(True)
            self.do_buttons[idx].setChecked(not checked)
            self.do_buttons[idx].setText("断开" if checked else "闭合")
            self.do_buttons[idx].blockSignals(False)


    # 处理ModbusWorker报告的继电器手动操作成功
    def handle_manual_relay_action_success(self, idx, value):
        # 这个槽函数确保ModbusWorker确认写入成功后才记录日志
        self.log(f"DO{idx + 1} 手动 {'闭合' if value else '断开'} 成功")
        self.record_relay_action(idx + 1, "闭合" if value else "断开", "手动")
        self.last_relay_ui_states[idx] = value # 更新最后记录的状态

    # 更新ModbusWorker线程中的自动控制设置
    def update_auto_control_settings(self):
        if self.modbus_worker:
            settings = []
            for i in range(4):
                auto_enabled = self.auto_checkboxes[i].isChecked()
                start_time_q = self.start_time_edits[i].time()
                end_time_q = self.end_time_edits[i].time()
                settings.append((auto_enabled, start_time_q, end_time_q))
            # 将设置直接传递给worker线程的属性，worker线程会在下次轮询时读取
            self.modbus_worker.auto_control_settings = settings
            self.log("🕒 已更新继电器定时控制设置")


    def select_save_directory(self):
        folder = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if folder:
            self.save_path = folder
            self.start_save_btn.setEnabled(True)
            self.log(f"📁 选择保存目录: {folder}")

    def start_saving(self):
        if not self.save_path:
            QMessageBox.warning(self, "保存目录未选择", "请先选择保存目录！")
            return
        self.save_data = True
        self.start_save_btn.setEnabled(False)
        self.stop_save_btn.setEnabled(True)
        self.log("💾 开始保存温湿度数据")

    def stop_saving(self):
        self.save_data = False
        self.start_save_btn.setEnabled(True)
        self.stop_save_btn.setEnabled(False)
        self.log("🛑 停止保存温湿度数据")

    def record_relay_action(self, do_num, action, action_type):
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.relay_action_log.append((now, f"DO{do_num}", action, action_type))
        self.log(f"📝 记录继电器动作: DO{do_num} {action} ({action_type})")

    def record_run_status(self):
        # 判断ModbusWorker是否在运行并已连接
        connected = self.modbus_worker and self.modbus_worker.client and self.modbus_worker.client.connected
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.run_status_log.append((now, connected))
        status_str = "正常" if connected else "异常"
        self.log(f"📊 运行状态记录: {now} - {status_str}")

    def export_relay_action_log(self):
        if not self.relay_action_log:
            QMessageBox.information(self, "导出日志", "继电器动作日志为空！")
            return
        
        # 建议文件名包含时间戳
        default_filename = f"继电器动作日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        file_path, _ = QFileDialog.getSaveFileName(self, "保存继电器动作日志", default_filename, "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            with open(file_path, mode='w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(["时间", "继电器", "动作", "类型"])
                writer.writerows(self.relay_action_log)
            self.log(f"✅ 继电器动作日志已导出: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出继电器动作日志失败: {e}")

    def export_run_status_log(self):
        if not self.run_status_log:
            QMessageBox.information(self, "导出日志", "运行状态日志为空！")
            return
        
        # 建议文件名包含时间戳
        default_filename = f"运行状态日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        file_path, _ = QFileDialog.getSaveFileName(self, "保存运行状态日志", default_filename, "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            with open(file_path, mode='w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(["时间", "状态"])
                for time_str, status in self.run_status_log:
                    writer.writerow([time_str, "正常" if status else "异常"])
            self.log(f"✅ 运行状态日志已导出: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出运行状态日志失败: {e}")

    def log(self, msg):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {msg}")

    def closeEvent(self, event):
        # 确保在窗口关闭时停止Modbus工作线程
        if self.modbus_worker and self.modbus_worker.isRunning():
            self.modbus_worker.stop()
            self.modbus_worker.wait() # 等待线程结束
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())