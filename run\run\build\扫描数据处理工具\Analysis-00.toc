(['D:\\代码\\张钰润-代码\\黑球数据处理代码\\run\\run\\scan_processor_gui.py'],
 ['D:\\代码\\张钰润-代码\\黑球数据处理代码\\run\\run'],
 [],
 [('D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.21 (main, Dec 11 2024, 16:35:24) [MSC v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('scan_processor_gui',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\run\\run\\scan_processor_gui.py',
   'PYSOURCE')],
 [('subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\subprocess.py',
   'PYMODULE'),
  ('selectors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\selectors.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextlib.py',
   'PYMODULE'),
  ('signal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\signal.py', 'PYMODULE'),
  ('_strptime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_strptime.py',
   'PYMODULE'),
  ('calendar', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gettext.py', 'PYMODULE'),
  ('struct', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\struct.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ipaddress.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\mimetypes.py',
   'PYMODULE'),
  ('getopt', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\getopt.py', 'PYMODULE'),
  ('email.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\quopri.py', 'PYMODULE'),
  ('email.errors',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('random', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\statistics.py',
   'PYMODULE'),
  ('fractions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\numbers.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ssl', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('string', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\string.py', 'PYMODULE'),
  ('hashlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hashlib.py', 'PYMODULE'),
  ('email',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('uu', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\email\\header.py',
   'PYMODULE'),
  ('bisect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bisect.py', 'PYMODULE'),
  ('xml.sax',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\contextvars.py',
   'PYMODULE'),
  ('base64', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\base64.py', 'PYMODULE'),
  ('hmac', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\hmac.py', 'PYMODULE'),
  ('socket', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tempfile.py', 'PYMODULE'),
  ('logging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipimport.py',
   'PYMODULE'),
  ('pathlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pathlib.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\typing.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bz2.py', 'PYMODULE'),
  ('tokenize', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('inspect', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ast.py', 'PYMODULE'),
  ('importlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_py_abc.py', 'PYMODULE'),
  ('PIL.Image',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('numpy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('doctest', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tty.py', 'PYMODULE'),
  ('readline',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\smtplib.py', 'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('glob', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\glob.py', 'PYMODULE'),
  ('code', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.case',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('threadpoolctl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('json',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\dataclasses.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('platform', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\platform.py', 'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('__future__',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\__future__.py',
   'PYMODULE'),
  ('PIL',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('threading',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_threading_local.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('csv', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\csv.py', 'PYMODULE'),
  ('shutil', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\tarfile.py', 'PYMODULE'),
  ('datetime',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\datetime.py',
   'PYMODULE')],
 [('python39.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python39.dll', 'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_webp.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_imagingtk.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_imagingcms.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_philox.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\random\\_common.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\yaml\\_yaml.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_imagingmath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp39-win_amd64.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\PIL\\_imaging.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libffi-7.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\python3.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\DLLs\\tk86t.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\py\\Anaconda\\envs\\pinjiee\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('numpy.libs\\.load-order-numpy-1.26.4',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\numpy.libs\\.load-order-numpy-1.26.4',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\py\\Anaconda\\envs\\pinjiee\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'D:\\代码\\张钰润-代码\\黑球数据处理代码\\run\\run\\build\\扫描数据处理工具\\base_library.zip',
   'DATA')],
 [('genericpath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\genericpath.py',
   'PYMODULE'),
  ('re', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\re.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('sre_constants',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_constants.py',
   'PYMODULE'),
  ('weakref', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\weakref.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('codecs', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\codecs.py', 'PYMODULE'),
  ('_bootlocale',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\_bootlocale.py',
   'PYMODULE'),
  ('sre_parse',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_parse.py',
   'PYMODULE'),
  ('linecache',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\linecache.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('types', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\types.py', 'PYMODULE'),
  ('functools',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\functools.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('warnings', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\warnings.py', 'PYMODULE'),
  ('ntpath', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\ntpath.py', 'PYMODULE'),
  ('reprlib', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\reprlib.py', 'PYMODULE'),
  ('copyreg', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\copyreg.py', 'PYMODULE'),
  ('keyword', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\keyword.py', 'PYMODULE'),
  ('enum', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\enum.py', 'PYMODULE'),
  ('abc', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\abc.py', 'PYMODULE'),
  ('posixpath',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\posixpath.py',
   'PYMODULE'),
  ('heapq', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\heapq.py', 'PYMODULE'),
  ('operator', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\operator.py', 'PYMODULE'),
  ('stat', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\stat.py', 'PYMODULE'),
  ('io', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\io.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\sre_compile.py',
   'PYMODULE'),
  ('locale', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\locale.py', 'PYMODULE'),
  ('traceback',
   'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\traceback.py',
   'PYMODULE'),
  ('os', 'D:\\py\\Anaconda\\envs\\pinjiee\\lib\\os.py', 'PYMODULE')])
