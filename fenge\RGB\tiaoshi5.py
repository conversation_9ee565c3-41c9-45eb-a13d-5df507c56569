import cv2
import numpy as np
import math
import os

def calculate_LAI(GF, theta_deg=0, k=0.5):
    if GF <= 0 or GF >= 1:
        raise ValueError("GF must be between 0 and 1 (exclusive)")
    theta_rad = math.radians(theta_deg)
    LAI = math.cos(theta_rad) * math.log(GF) / k
    return -LAI

# ---------------------- 主程序 -----------------------

# 图像路径
img_path = 'D:/fenge/RGB.png'
img = cv2.imread(img_path)
if img is None:
    raise FileNotFoundError(f"图像路径不存在: {img_path}")

# 提取图像文件名前缀
img_name = os.path.splitext(os.path.basename(img_path))[0]  # pano20
output_dir = os.path.dirname(img_path)  # D:/fenge

# 转HSV
hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

# 原始绿色分割范围
lower_green = np.array([35, 40, 40])
upper_green = np.array([85, 255, 255])
mask_green = cv2.inRange(hsv, lower_green, upper_green)

# 分割图像
result_vegetation = cv2.bitwise_and(img, img, mask=mask_green)
mask_ground = cv2.bitwise_not(mask_green)
result_ground = cv2.bitwise_and(img, img, mask=mask_ground)

# 统计像素
n_total = img.shape[0] * img.shape[1]
n_ground = cv2.countNonZero(mask_ground)
n_vegetation = cv2.countNonZero(mask_green)
GF = n_ground / n_total

try:
    LAI = calculate_LAI(GF, theta_deg=0, k=0.5)
except ValueError as e:
    print(f"计算LAI失败: {e}")
    LAI = None

# 保存图像（添加前缀）
cv2.imwrite(os.path.join(output_dir, f'{img_name}_segmented_vegetation.bmp'), result_vegetation)
cv2.imwrite(os.path.join(output_dir, f'{img_name}_segmented_ground.bmp'), result_ground)
cv2.imwrite(os.path.join(output_dir, f'{img_name}_mask_green.png'), mask_green)
cv2.imwrite(os.path.join(output_dir, f'{img_name}_mask_ground.png'), mask_ground)

# 保存LAI值
with open(os.path.join(output_dir, f'{img_name}_LAI_result.txt'), 'w') as f:
    f.write(f"图像路径: {img_path}\n")
    f.write(f"图像大小: {img.shape[1]} x {img.shape[0]} (宽 x 高)\n")
    f.write(f"总像素数: {n_total}\n")
    f.write(f"地面像素数: {n_ground}\n")
    f.write(f"植被像素数: {n_vegetation}\n")
    f.write(f"叶片孔隙率GF: {GF:.4f}\n")
    if LAI is not None:
        f.write(f"计算得到的LAI: {LAI:.4f}\n")
    else:
        f.write("LAI计算失败,GF值异常。\n")

print(f"分割图像与LAI计算结果已保存到 {output_dir}")
print(f"LAI = {LAI:.4f}" if LAI is not None else "LAI计算失败。")
