import os
import cv2

def downsample_images(input_dir, output_dir, scale=2):
    """
    对文件夹中的所有图像进行降采样处理并保存到输出文件夹。

    :param input_dir: 输入图像文件夹路径
    :param output_dir: 降采样后图像的保存路径
    :param scale: 降采样等级（2 表示长宽缩小为原来的 1/2）
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 支持的图像格式
    valid_exts = ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff']

    for filename in os.listdir(input_dir):
        ext = os.path.splitext(filename)[1].lower()
        if ext in valid_exts:
            input_path = os.path.join(input_dir, filename)
            img = cv2.imread(input_path)
            if img is None:
                print(f"跳过无法读取的图像：{input_path}")
                continue

            # 计算新尺寸
            h, w = img.shape[:2]
            new_w, new_h = w // scale, h // scale
            downsampled = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)

            output_path = os.path.join(output_dir, filename)
            cv2.imwrite(output_path, downsampled)
            print(f"保存降采样图像：{output_path}")

if __name__ == "__main__":
    # ===== 在此处修改路径和降采样等级 =====
    input_dir = r"D:\daima\gitup\7month\image-stitching-main\photo_RGB"        # 输入图像文件夹
    output_dir = r"D:\daima\gitup\7month\image-stitching-main\photo_RGB_downsample"     # 降采样后保存的文件夹
    scale = 2  # 降采样等级（例如2表示缩小为1/2）

    downsample_images(input_dir, output_dir, scale)
